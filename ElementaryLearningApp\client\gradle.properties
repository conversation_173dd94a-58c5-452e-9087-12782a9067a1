# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# https://docs.gradle.org/current/userguide/build_environment.html

# The Gradle Daemon speeds up builds by reusing processes.
# This is enabled by default, but we can use this to explicitally enable it.
org.gradle.daemon=true

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8

# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# https://docs.gradle.org/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true

# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app"s APK
# https://developer.android.com/jetpack/androidx/
android.useAndroidX=true

# Automatically convert third-party libraries to use AndroidX
android.enableJetifier=false

# Enables a memory-efficient dexing tool that can reduce build times.
# https://developer.android.com/studio/build/multidex
# android.enableD8.desugaring=true 