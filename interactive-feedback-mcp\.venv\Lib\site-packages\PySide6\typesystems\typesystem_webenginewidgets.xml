<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtWebEngineWidgets" doc-package="PySide6.QtWebEngine"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
  <load-typesystem name="typesystem_core.xml" generate="no"/>
  <load-typesystem name="typesystem_gui.xml" generate="no"/>
  <load-typesystem name="typesystem_widgets.xml" generate="no"/>
  <load-typesystem name="typesystem_network.xml" generate="no"/>
  <load-typesystem name="typesystem_webenginecore.xml" generate="no"/>
  <load-typesystem name="typesystem_printsupport.xml" generate="no"/>

  <object-type name="QWebEngineView">
      <add-function signature="findText(const QString &amp;@subString@,QWebEnginePage::FindFlags@options@,PyCallable*@resultCallback@)">
          <inject-code class="target" position="beginning" file="../glue/qtwebenginecore.cpp"
                       snippet="qwebenginepage-findtext"/>
      </add-function>
  </object-type>

</typesystem>
