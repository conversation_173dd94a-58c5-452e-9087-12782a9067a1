package com.example.elementarylearningcompanion

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.elementarylearningcompanion.ui.theme.ElementaryLearningCompanionTheme
import com.example.elementarylearningcompanion.viewmodel.AuthViewModel

class MainActivity : ComponentActivity() {
    private val viewModel: AuthViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ElementaryLearningCompanionTheme {
                Surface(modifier = Modifier.fillMaxSize(), color = MaterialTheme.colorScheme.background) {
                    AuthScreen(viewModel)
                }
            }
        }
    }
}

@Composable
fun AuthScreen(viewModel: AuthViewModel) {
    var phoneNumber by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    val context = LocalContext.current

    val authResult by viewModel.authResult.observeAsState()
    val isLoading by viewModel.isLoading.observeAsState(false)

    LaunchedEffect(authResult) {
        authResult?.let {
            val message = it.getMessage()
            Toast.makeText(context, message, Toast.LENGTH_LONG).show()

            if (it.isSuccess() && message.contains("Login")) {
                context.startActivity(Intent(context, OnboardingActivity::class.java))
            }
        }
    }

    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(text = "欢迎使用小学伴读", style = MaterialTheme.typography.headlineMedium)

            Spacer(modifier = Modifier.height(32.dp))

            OutlinedTextField(
                value = phoneNumber,
                onValueChange = { phoneNumber = it },
                label = { Text("手机号") },
                modifier = Modifier.fillMaxWidth(),
                enabled = !isLoading
            )

            Spacer(modifier = Modifier.height(16.dp))

            OutlinedTextField(
                value = password,
                onValueChange = { password = it },
                label = { Text("密码") },
                visualTransformation = PasswordVisualTransformation(),
                modifier = Modifier.fillMaxWidth(),
                enabled = !isLoading
            )

            Spacer(modifier = Modifier.height(32.dp))

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Button(
                    onClick = { viewModel.login(phoneNumber, password) },
                    enabled = !isLoading
                ) {
                    Text("登录")
                }
                Button(
                    onClick = { viewModel.register(phoneNumber, password) },
                    enabled = !isLoading
                ) {
                    Text("注册")
                }
            }
        }
        if (isLoading) {
            CircularProgressIndicator()
        }
    }
}

@Preview(showBackground = true)
@Composable
fun AuthScreenPreview() {
    ElementaryLearningCompanionTheme {
       Surface(modifier = Modifier.fillMaxSize()) {
           // This is a static preview, so we don't pass a real ViewModel
           // We can simulate states for previewing
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(text = "欢迎使用小学伴读", style = MaterialTheme.typography.headlineMedium)
                    Spacer(modifier = Modifier.height(32.dp))
                    OutlinedTextField(value = "1234567890", onValueChange = {}, label = { Text("手机号") })
                    Spacer(modifier = Modifier.height(16.dp))
                    OutlinedTextField(value = "password", onValueChange = {}, label = { Text("密码") })
                }
            }
       }
    }
}
