package com.example.elementarylearningcompanion.service;

import com.example.elementarylearningcompanion.model.Parent;
import com.example.elementarylearningcompanion.repository.ParentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;

@Service
public class AuthService {

    @Autowired
    private ParentRepository parentRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * Registers a new parent user.
     * @param phoneNumber The parent's phone number.
     * @param password The parent's plain text password.
     * @return The created Parent object.
     * @throws Exception if phone number already exists.
     */
    public Parent registerParent(String phoneNumber, String password) throws Exception {
        if (parentRepository.findByPhoneNumber(phoneNumber).isPresent()) {
            throw new Exception("Phone number is already registered.");
        }

        String hashedPassword = passwordEncoder.encode(password);

        Parent newParent = new Parent();
        newParent.setPhoneNumber(phoneNumber);
        newParent.setPasswordHash(hashedPassword);
        newParent.setCreatedAt(LocalDateTime.now());
        newParent.setUpdatedAt(LocalDateTime.now());

        return parentRepository.save(newParent);
    }

    /**
     * Authenticates a parent.
     * @param phoneNumber The parent's phone number.
     * @param password The parent's plain text password.
     * @return true if login is successful, false otherwise.
     */
    public boolean login(String phoneNumber, String password) {
        var parentOptional = parentRepository.findByPhoneNumber(phoneNumber);
        if (parentOptional.isEmpty()) {
            return false; // User not found
        }

        Parent parent = parentOptional.get();
        return passwordEncoder.matches(password, parent.getPasswordHash());
    }
}
