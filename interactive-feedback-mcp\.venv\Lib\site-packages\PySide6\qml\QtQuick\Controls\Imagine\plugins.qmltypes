import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "qquickattachedpropertypropagator.h"
        name: "QQuickAttachedPropertyPropagator"
        accessSemantics: "reference"
        prototype: "QObject"
    }
    Component {
        file: "private/qquickimaginestyle_p.h"
        name: "QQuickImagineStyle"
        accessSemantics: "reference"
        prototype: "QQuickAttachedPropertyPropagator"
        exports: [
            "QtQuick.Controls.Imagine/Imagine 2.3",
            "QtQuick.Controls.Imagine/Imagine 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [515, 1536]
        attachedType: "QQuickImagineStyle"
        Property {
            name: "path"
            type: "QString"
            read: "path"
            write: "setPath"
            reset: "resetPath"
            notify: "pathChanged"
            index: 0
            isFinal: true
        }
        Property {
            name: "url"
            type: "QUrl"
            read: "url"
            notify: "pathChanged"
            index: 1
            isReadonly: true
            isFinal: true
        }
        Signal { name: "pathChanged" }
    }
}
