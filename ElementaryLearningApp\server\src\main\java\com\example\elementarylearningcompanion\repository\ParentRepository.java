package com.example.elementarylearningcompanion.repository;

import com.example.elementarylearningcompanion.model.Parent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ParentRepository extends JpaRepository<Parent, Long> {

    Optional<Parent> findByPhoneNumber(String phoneNumber);
}
