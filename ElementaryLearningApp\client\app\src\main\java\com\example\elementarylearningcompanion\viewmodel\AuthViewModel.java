package com.example.elementarylearningcompanion.viewmodel;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import com.example.elementarylearningcompanion.dto.ApiResponse;
import com.example.elementarylearningcompanion.dto.AuthRequest;
import com.example.elementarylearningcompanion.network.ApiService;
import com.example.elementarylearningcompanion.network.RetrofitClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class AuthViewModel extends ViewModel {
    private final ApiService apiService = RetrofitClient.getClient().create(ApiService.class);

    private final MutableLiveData<ApiResponse> _authResult = new MutableLiveData<>();
    public LiveData<ApiResponse> authResult = _authResult;

    private final MutableLiveData<Boolean> _isLoading = new MutableLiveData<>(false);
    public LiveData<Boolean> isLoading = _isLoading;

    public void login(String phoneNumber, String password) {
        _isLoading.setValue(true);
        apiService.login(new AuthRequest(phoneNumber, password)).enqueue(new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if(response.isSuccessful()) {
                    _authResult.postValue(response.body());
                } else {
                    _authResult.postValue(new ApiResponse(false, "Login failed: " + response.code()));
                }
                _isLoading.setValue(false);
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                _authResult.postValue(new ApiResponse(false, "Network error: " + t.getMessage()));
                _isLoading.setValue(false);
            }
        });
    }

    public void register(String phoneNumber, String password) {
        _isLoading.setValue(true);
        apiService.registerParent(new AuthRequest(phoneNumber, password)).enqueue(new Callback<ApiResponse>() {
            @Override
            public void onResponse(Call<ApiResponse> call, Response<ApiResponse> response) {
                if(response.isSuccessful()) {
                    _authResult.postValue(response.body());
                } else {
                    _authResult.postValue(new ApiResponse(false, "Registration failed: " + response.code()));
                }
                _isLoading.setValue(false);
            }

            @Override
            public void onFailure(Call<ApiResponse> call, Throwable t) {
                _authResult.postValue(new ApiResponse(false, "Network error: " + t.getMessage()));
                _isLoading.setValue(false);
            }
        });
    }
}
