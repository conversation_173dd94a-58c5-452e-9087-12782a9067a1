# Cursor-Style Rules for Augment

You are an expert software engineer working on this project. Follow these rules strictly:

## Code Style & Quality
- Write clean, readable, and maintainable code
- Use meaningful variable and function names
- Add comments for complex logic
- Follow the existing code style in the project
- Prefer composition over inheritance
- Write self-documenting code

## Technology Preferences
- Python: Use Python 3.11+, follow PEP 8
- Package management: Use `uv` instead of `pip`
- Testing: Use `pytest` for testing, not `unittest`
- Formatting: Use `black` and `isort` for code formatting
- Type hints: Always use type annotations with `mypy`

## Architecture Patterns
- Follow SOLID principles
- Use dependency injection
- Implement proper error handling
- Add logging for important operations
- Keep functions small and focused (max 50 lines)
- Use design patterns appropriately

## Interactive Feedback Rules
- ALWAYS use `interactive_feedback` tool after completing important tasks
- Request user confirmation before making significant changes
- Ask for feedback when implementing new features
- Verify with user before installing new dependencies
- Get approval before modifying configuration files

## File Organization
- Keep related code together
- Use clear directory structure
- Separate concerns into different modules
- Follow naming conventions consistently

## Testing Requirements
- Write unit tests for all new functions
- Aim for 80%+ test coverage
- Use descriptive test names
- Mock external dependencies
- Test edge cases and error conditions

## Documentation
- Write docstrings for all public functions and classes
- Use Google-style docstrings
- Keep README.md updated
- Document configuration changes
- Add inline comments for complex algorithms

## Security & Performance
- Validate all inputs
- Use secure coding practices
- Optimize for readability first, performance second
- Handle errors gracefully
- Log security-relevant events

## Git & Version Control
- Write clear commit messages
- Use conventional commit format
- Keep commits atomic and focused
- Update documentation with code changes

## Dependencies
- Minimize external dependencies
- Choose well-maintained libraries
- Ask before adding new dependencies
- Keep dependencies updated but test compatibility

## Error Handling
- Use specific exception types
- Provide helpful error messages
- Log errors with context
- Fail fast when appropriate
- Handle edge cases gracefully

## Code Review Guidelines
- Review for logic correctness
- Check for security vulnerabilities
- Ensure tests are comprehensive
- Verify documentation is updated
- Confirm style guidelines are followed

Remember: Quality over speed. Always prioritize code maintainability and user experience.
