#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Interactive Feedback MCP测试脚本
"""

import json
import subprocess
import sys
import time
import os
from pathlib import Path

def test_basic():
    """基本功能测试"""
    print("=== Interactive Feedback MCP 集成测试 ===")
    
    # 1. 检查MCP目录
    mcp_dir = Path("f:/study/interactive-feedback-mcp")
    print(f"1. 检查MCP目录: {mcp_dir}")
    if mcp_dir.exists():
        print("   [OK] MCP目录存在")
    else:
        print("   [FAIL] MCP目录不存在")
        return False
    
    # 2. 测试uv命令
    print("2. 测试uv命令")
    try:
        result = subprocess.run(["uv", "--version"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"   [OK] uv版本: {result.stdout.strip()}")
        else:
            print("   [FAIL] uv命令不可用")
            return False
    except Exception as e:
        print(f"   [FAIL] uv测试失败: {e}")
        return False
    
    # 3. 测试Python模块导入
    print("3. 测试Python模块导入")
    try:
        result = subprocess.run([
            "uv", "run", "python", "-c", 
            "import server; print('Server module OK')"
        ], 
        cwd=mcp_dir, 
        capture_output=True, 
        text=True, 
        timeout=30
        )
        
        if result.returncode == 0:
            print("   [OK] 服务器模块导入成功")
        else:
            print(f"   [FAIL] 模块导入失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"   [FAIL] 模块测试异常: {e}")
        return False
    
    # 4. 检查配置文件
    print("4. 检查配置文件")
    config_file = Path("augment-mcp-config.json")
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            print("   [OK] 配置文件格式正确")
        except Exception as e:
            print(f"   [FAIL] 配置文件错误: {e}")
            return False
    else:
        print("   [FAIL] 配置文件不存在")
        return False
    
    print("\n=== 测试完成 ===")
    print("所有基本测试通过！")
    print("\n下一步:")
    print("1. 将 augment-mcp-config.json 内容添加到Augment的MCP配置")
    print("2. 重启Augment")
    print("3. 测试 interactive_feedback 工具")
    
    return True

if __name__ == "__main__":
    test_basic()
