[tool:pytest]
# pytest 配置文件

# 測試目錄
testpaths = tests

# 最小版本要求
minversion = 6.0

# 添加選項
addopts =
    --strict-markers
    --strict-config
    --disable-warnings
    --tb=short
    -ra

# 標記定義
markers =
    unit: 單元測試
    integration: 集成測試
    i18n: 國際化測試
    web: Web UI 測試
    mcp: MCP 相關測試
    slow: 慢速測試
    asyncio: 異步測試

# 測試發現模式
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 異步測試支援
asyncio_mode = auto

# 超時設置（秒）
timeout = 60

# 過濾警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:aiohttp.*
