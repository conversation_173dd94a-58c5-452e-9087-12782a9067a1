"""
Augment Interactive Feedback Tool
基于 Interactive Feedback MCP 的 Augment 原生实现

这个工具允许 Augment Agent 在执行任务时主动请求用户反馈，
实现人机交互循环，提升AI辅助开发的体验和效果。
"""

import os
import sys
import json
import tempfile
import subprocess
import threading
import time
from typing import Dict, Any, Optional
from pathlib import Path

class AugmentInteractiveFeedback:
    """Augment 交互式反馈工具"""
    
    def __init__(self):
        self.feedback_ui_process = None
        self.feedback_result = None
        
    def request_feedback(
        self, 
        project_directory: str = ".", 
        summary: str = "我已完成了您请求的任务，请查看并提供反馈。",
        timeout: int = 600
    ) -> Dict[str, Any]:
        """
        请求用户交互式反馈
        
        Args:
            project_directory: 项目目录路径
            summary: AI工作完成的摘要说明
            timeout: 等待用户反馈的超时时间（秒）
            
        Returns:
            包含用户反馈和命令日志的字典
        """
        
        # 验证项目目录
        if not os.path.exists(project_directory):
            return {
                "error": f"项目目录不存在: {project_directory}",
                "feedback": "",
                "logs": ""
            }
            
        # 创建临时文件用于接收反馈结果
        with tempfile.NamedTemporaryFile(suffix=".json", delete=False) as tmp:
            output_file = tmp.name
            
        try:
            # 启动反馈UI
            result = self._launch_feedback_ui(
                project_directory, 
                summary, 
                output_file,
                timeout
            )
            
            if result:
                return {
                    "feedback": result.get("interactive_feedback", ""),
                    "logs": result.get("command_logs", ""),
                    "success": True
                }
            else:
                return {
                    "feedback": "",
                    "logs": "",
                    "success": False,
                    "error": "用户取消或超时"
                }
                
        except Exception as e:
            return {
                "error": f"启动反馈UI失败: {str(e)}",
                "feedback": "",
                "logs": "",
                "success": False
            }
        finally:
            # 清理临时文件
            if os.path.exists(output_file):
                try:
                    os.unlink(output_file)
                except:
                    pass
    
    def _launch_feedback_ui(
        self, 
        project_directory: str, 
        summary: str, 
        output_file: str,
        timeout: int
    ) -> Optional[Dict[str, Any]]:
        """启动反馈UI并等待结果"""
        
        # 检查是否有可用的反馈UI实现
        feedback_ui_path = self._find_feedback_ui()
        if not feedback_ui_path:
            # 如果没有GUI，使用命令行fallback
            return self._command_line_feedback(project_directory, summary)
            
        try:
            # 启动GUI反馈界面
            args = [
                sys.executable,
                "-u",
                feedback_ui_path,
                "--project-directory", project_directory,
                "--prompt", summary,
                "--output-file", output_file
            ]
            
            process = subprocess.Popen(
                args,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                stdin=subprocess.DEVNULL,
                close_fds=True
            )
            
            # 等待进程完成或超时
            start_time = time.time()
            while process.poll() is None:
                if time.time() - start_time > timeout:
                    process.terminate()
                    return None
                time.sleep(0.1)
                
            # 读取结果
            if os.path.exists(output_file):
                with open(output_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
                    
        except Exception as e:
            print(f"GUI反馈界面启动失败: {e}")
            # 降级到命令行模式
            return self._command_line_feedback(project_directory, summary)
            
        return None
    
    def _find_feedback_ui(self) -> Optional[str]:
        """查找可用的反馈UI实现"""
        
        # 检查是否有已安装的interactive-feedback-mcp
        possible_paths = [
            # 当前目录下的interactive-feedback-mcp
            os.path.join(os.getcwd(), "interactive-feedback-mcp", "feedback_ui.py"),
            # 用户目录下的安装
            os.path.expanduser("~/interactive-feedback-mcp/feedback_ui.py"),
            # 系统安装路径
            "/opt/interactive-feedback-mcp/feedback_ui.py",
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
                
        return None
    
    def _command_line_feedback(
        self, 
        project_directory: str, 
        summary: str
    ) -> Dict[str, Any]:
        """命令行模式的反馈收集（fallback）"""
        
        print(f"\n{'='*60}")
        print(f"📋 AI工作摘要: {summary}")
        print(f"📁 项目目录: {project_directory}")
        print(f"{'='*60}")
        
        # 询问是否需要执行命令
        print("\n🔧 是否需要执行命令来验证结果？(y/n): ", end="")
        need_command = input().strip().lower() == 'y'
        
        command_logs = ""
        if need_command:
            print("💻 请输入要执行的命令: ", end="")
            command = input().strip()
            if command:
                try:
                    result = subprocess.run(
                        command,
                        shell=True,
                        cwd=project_directory,
                        capture_output=True,
                        text=True,
                        timeout=60
                    )
                    command_logs = f"$ {command}\n{result.stdout}\n{result.stderr}"
                    print(f"命令执行完成，返回码: {result.returncode}")
                except Exception as e:
                    command_logs = f"$ {command}\n执行失败: {str(e)}"
                    print(f"命令执行失败: {e}")
        
        # 收集用户反馈
        print(f"\n💬 请提供您的反馈（输入完成后按回车）:")
        print("   - 如果满意，可以直接按回车")
        print("   - 如果需要修改，请详细说明")
        print("反馈内容: ", end="")
        
        feedback = input().strip()
        
        return {
            "interactive_feedback": feedback,
            "command_logs": command_logs
        }


# Augment工具函数接口
def interactive_feedback(
    project_directory: str = ".",
    summary: str = "我已完成了您请求的任务，请查看并提供反馈。",
    timeout: int = 600
) -> Dict[str, Any]:
    """
    Augment交互式反馈工具
    
    这个工具允许AI在完成任务后主动请求用户反馈，实现人机交互循环。
    
    Args:
        project_directory: 项目目录路径，默认为当前目录
        summary: AI工作完成的摘要说明
        timeout: 等待用户反馈的超时时间（秒），默认10分钟
        
    Returns:
        包含用户反馈、命令日志和执行状态的字典
        
    Example:
        result = interactive_feedback(
            project_directory="/path/to/project",
            summary="我已经实现了用户登录功能，请测试并提供反馈"
        )
        
        if result["success"]:
            user_feedback = result["feedback"]
            command_logs = result["logs"]
            # 根据反馈继续处理...
    """
    
    tool = AugmentInteractiveFeedback()
    return tool.request_feedback(project_directory, summary, timeout)


# 使用示例
if __name__ == "__main__":
    # 测试工具
    result = interactive_feedback(
        project_directory=".",
        summary="我已经创建了Augment交互式反馈工具，请测试功能是否正常"
    )
    
    print("\n📊 反馈结果:")
    print(f"成功: {result.get('success', False)}")
    print(f"反馈: {result.get('feedback', 'N/A')}")
    print(f"日志: {result.get('logs', 'N/A')}")
    if 'error' in result:
        print(f"错误: {result['error']}")
