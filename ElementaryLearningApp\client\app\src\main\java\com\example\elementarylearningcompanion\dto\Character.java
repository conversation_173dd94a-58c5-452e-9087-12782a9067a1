package com.example.elementarylearningcompanion.dto;

public class Character {
    private long id;
    private String characterText;
    private String pinyin;
    private int strokes;
    private String radical;
    private String structure;
    private String audioUrl;
    private String strokeAnimationUrl;

    // Getters and Setters
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }
    public String getCharacterText() { return characterText; }
    public void setCharacterText(String characterText) { this.characterText = characterText; }
    public String getPinyin() { return pinyin; }
    public void setPinyin(String pinyin) { this.pinyin = pinyin; }
    public int getStrokes() { return strokes; }
    public void setStrokes(int strokes) { this.strokes = strokes; }
    public String getRadical() { return radical; }
    public void setRadical(String radical) { this.radical = radical; }
    public String getStructure() { return structure; }
    public void setStructure(String structure) { this.structure = structure; }
    public String getAudioUrl() { return audioUrl; }
    public void setAudioUrl(String audioUrl) { this.audioUrl = audioUrl; }
    public String getStrokeAnimationUrl() { return strokeAnimationUrl; }
    public void setStrokeAnimationUrl(String strokeAnimationUrl) { this.strokeAnimationUrl = strokeAnimationUrl; }
}
