package com.example.elementarylearningcompanion.network;

import com.example.elementarylearningcompanion.dto.ApiResponse;
import com.example.elementarylearningcompanion.dto.AuthRequest;
import com.example.elementarylearningcompanion.dto.Character;
import com.example.elementarylearningcompanion.dto.Lesson;
import com.example.elementarylearningcompanion.dto.TextbookVersion;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;
import retrofit2.http.Query;

import java.util.List;

public interface ApiService {

    @POST("api/auth/register")
    Call<ApiResponse> registerParent(@Body AuthRequest authRequest);

    @POST("api/auth/login")
    Call<ApiResponse> login(@Body AuthRequest authRequest);

    @GET("api/content/textbooks")
    Call<List<TextbookVersion>> getTextbookVersions(@Query("subject") String subject);

    @GET("api/content/lessons")
    Call<List<Lesson>> getLessons(@Query("textbookVersionId") int textbookVersionId, @Query("grade") int grade);

    @GET("api/content/lessons/{id}")
    Call<Lesson> getLessonDetails(@Path("id") long lessonId);

    @GET("api/content/lessons/{lessonId}/characters")
    Call<List<Character>> getCharactersByLesson(@Path("lessonId") long lessonId);
}
