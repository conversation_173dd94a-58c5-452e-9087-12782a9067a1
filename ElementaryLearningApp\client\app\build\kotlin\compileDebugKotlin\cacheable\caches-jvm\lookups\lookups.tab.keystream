  Activity android.app  
AuthScreen android.app.Activity  
AuthViewModel android.app.Activity  Bundle android.app.Activity  ContentViewModel android.app.Activity   ElementaryLearningCompanionTheme android.app.Activity  LessonScreen android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  OnboardingScreen android.app.Activity  Surface android.app.Activity  fillMaxSize android.app.Activity  getValue android.app.Activity  	getWINDOW android.app.Activity  	getWindow android.app.Activity  onCreate android.app.Activity  provideDelegate android.app.Activity  
setContent android.app.Activity  	setWindow android.app.Activity  
viewModels android.app.Activity  window android.app.Activity  Context android.content  Intent android.content  
AuthScreen android.content.Context  
AuthViewModel android.content.Context  Bundle android.content.Context  ContentViewModel android.content.Context   ElementaryLearningCompanionTheme android.content.Context  LessonScreen android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  OnboardingScreen android.content.Context  Surface android.content.Context  fillMaxSize android.content.Context  getValue android.content.Context  onCreate android.content.Context  provideDelegate android.content.Context  
setContent android.content.Context  
startActivity android.content.Context  
viewModels android.content.Context  
AuthScreen android.content.ContextWrapper  
AuthViewModel android.content.ContextWrapper  Bundle android.content.ContextWrapper  ContentViewModel android.content.ContextWrapper   ElementaryLearningCompanionTheme android.content.ContextWrapper  LessonScreen android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  OnboardingScreen android.content.ContextWrapper  Surface android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  getValue android.content.ContextWrapper  onCreate android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  
setContent android.content.ContextWrapper  
viewModels android.content.ContextWrapper  getLongExtra android.content.Intent  putExtra android.content.Intent  Build 
android.os  Bundle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  View android.view  Window android.view  
AuthScreen  android.view.ContextThemeWrapper  
AuthViewModel  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  ContentViewModel  android.view.ContextThemeWrapper   ElementaryLearningCompanionTheme  android.view.ContextThemeWrapper  LessonScreen  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  OnboardingScreen  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  
viewModels  android.view.ContextThemeWrapper  context android.view.View  
getCONTEXT android.view.View  
getContext android.view.View  getISInEditMode android.view.View  getIsInEditMode android.view.View  isInEditMode android.view.View  
setContext android.view.View  
setInEditMode android.view.View  getSTATUSBarColor android.view.Window  getStatusBarColor android.view.Window  setStatusBarColor android.view.Window  statusBarColor android.view.Window  Toast android.widget  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  
viewModels androidx.activity  
AuthScreen #androidx.activity.ComponentActivity  
AuthViewModel #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  ContentViewModel #androidx.activity.ComponentActivity   ElementaryLearningCompanionTheme #androidx.activity.ComponentActivity  LessonScreen #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  OnboardingScreen #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
viewModels #androidx.activity.ComponentActivity  
AuthScreen -androidx.activity.ComponentActivity.Companion   ElementaryLearningCompanionTheme -androidx.activity.ComponentActivity.Companion  LessonScreen -androidx.activity.ComponentActivity.Companion  
MaterialTheme -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  OnboardingScreen -androidx.activity.ComponentActivity.Companion  Surface -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  getFILLMaxSize -androidx.activity.ComponentActivity.Companion  getFillMaxSize -androidx.activity.ComponentActivity.Companion  getGETValue -androidx.activity.ComponentActivity.Companion  getGetValue -androidx.activity.ComponentActivity.Companion  getPROVIDEDelegate -androidx.activity.ComponentActivity.Companion  getProvideDelegate -androidx.activity.ComponentActivity.Companion  getValue -androidx.activity.ComponentActivity.Companion  provideDelegate -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
viewModels -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
AuthScreen "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  
CharacterItem "androidx.compose.foundation.layout  CharactersList "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout   ElementaryLearningCompanionTheme "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Intent "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LessonActivity "androidx.compose.foundation.layout  
LessonItem "androidx.compose.foundation.layout  LessonScreen "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OnboardingActivity "androidx.compose.foundation.layout  OnboardingScreen "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  PasswordVisualTransformation "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  SentenceItem "androidx.compose.foundation.layout  
SentencesList "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  TextbookItem "androidx.compose.foundation.layout  Toast "androidx.compose.foundation.layout  contains "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  items "androidx.compose.foundation.layout  java "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  
setContent "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  
viewModels "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  CharactersList +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  OutlinedTextField +androidx.compose.foundation.layout.BoxScope  PasswordVisualTransformation +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  
SentencesList +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  getFILLMaxSize +androidx.compose.foundation.layout.BoxScope  getFILLMaxWidth +androidx.compose.foundation.layout.BoxScope  getFillMaxSize +androidx.compose.foundation.layout.BoxScope  getFillMaxWidth +androidx.compose.foundation.layout.BoxScope  	getHEIGHT +androidx.compose.foundation.layout.BoxScope  	getHeight +androidx.compose.foundation.layout.BoxScope  getLET +androidx.compose.foundation.layout.BoxScope  getLet +androidx.compose.foundation.layout.BoxScope  
getPADDING +androidx.compose.foundation.layout.BoxScope  
getPadding +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  ArrowForward .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  CharactersList .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Intent .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LessonActivity .androidx.compose.foundation.layout.ColumnScope  
LessonItem .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  
SentencesList .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  TextbookItem .androidx.compose.foundation.layout.ColumnScope  Toast .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  java .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  ArrowForward +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  Toast +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  
CharacterItem .androidx.compose.foundation.lazy.LazyItemScope  Intent .androidx.compose.foundation.lazy.LazyItemScope  LessonActivity .androidx.compose.foundation.lazy.LazyItemScope  
LessonItem .androidx.compose.foundation.lazy.LazyItemScope  SentenceItem .androidx.compose.foundation.lazy.LazyItemScope  TextbookItem .androidx.compose.foundation.lazy.LazyItemScope  java .androidx.compose.foundation.lazy.LazyItemScope  
CharacterItem .androidx.compose.foundation.lazy.LazyListScope  Intent .androidx.compose.foundation.lazy.LazyListScope  LessonActivity .androidx.compose.foundation.lazy.LazyListScope  
LessonItem .androidx.compose.foundation.lazy.LazyListScope  SentenceItem .androidx.compose.foundation.lazy.LazyListScope  TextbookItem .androidx.compose.foundation.lazy.LazyListScope  getITEMS .androidx.compose.foundation.lazy.LazyListScope  getItems .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  java .androidx.compose.foundation.lazy.LazyListScope  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  ArrowForward ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  ArrowForward &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  
AuthScreen androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  Card androidx.compose.material3  
CharacterItem androidx.compose.material3  CharactersList androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3   ElementaryLearningCompanionTheme androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Intent androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  LessonActivity androidx.compose.material3  
LessonItem androidx.compose.material3  LessonScreen androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OnboardingActivity androidx.compose.material3  OnboardingScreen androidx.compose.material3  OutlinedTextField androidx.compose.material3  PasswordVisualTransformation androidx.compose.material3  Row androidx.compose.material3  SentenceItem androidx.compose.material3  
SentencesList androidx.compose.material3  Spacer androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  TextbookItem androidx.compose.material3  Toast androidx.compose.material3  
Typography androidx.compose.material3  contains androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  items androidx.compose.material3  java androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  
setContent androidx.compose.material3  setValue androidx.compose.material3  
viewModels androidx.compose.material3  
background &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
headlineLarge %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  
AuthScreen androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  Card androidx.compose.runtime  
CharacterItem androidx.compose.runtime  CharactersList androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime   ElementaryLearningCompanionTheme androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Intent androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LessonActivity androidx.compose.runtime  
LessonItem androidx.compose.runtime  LessonScreen androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OnboardingActivity androidx.compose.runtime  OnboardingScreen androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  PasswordVisualTransformation androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Row androidx.compose.runtime  SentenceItem androidx.compose.runtime  
SentencesList androidx.compose.runtime  
SideEffect androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  Surface androidx.compose.runtime  Text androidx.compose.runtime  TextbookItem androidx.compose.runtime  Toast androidx.compose.runtime  contains androidx.compose.runtime  	emptyList androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  items androidx.compose.runtime  java androidx.compose.runtime  let androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  
setContent androidx.compose.runtime  setValue androidx.compose.runtime  
viewModels androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  observeAsState !androidx.compose.runtime.livedata  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	clickable androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  getCLICKABLE androidx.compose.ui.Modifier  getClickable androidx.compose.ui.Modifier  	getHEIGHT androidx.compose.ui.Modifier  	getHeight androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  	getWEIGHT &androidx.compose.ui.Modifier.Companion  	getWeight &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  	getTOArgb "androidx.compose.ui.graphics.Color  	getToArgb "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  PasswordVisualTransformation androidx.compose.ui.text.input  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  
AuthScreen #androidx.core.app.ComponentActivity  
AuthViewModel #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  ContentViewModel #androidx.core.app.ComponentActivity   ElementaryLearningCompanionTheme #androidx.core.app.ComponentActivity  LessonScreen #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  OnboardingScreen #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
viewModels #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  getISAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  getIsAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  setAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  getOBSERVEAsState androidx.lifecycle.LiveData  getObserveAsState androidx.lifecycle.LiveData  observeAsState androidx.lifecycle.LiveData  fetchCharactersByLesson androidx.lifecycle.ViewModel  fetchLessonDetails androidx.lifecycle.ViewModel  fetchLessons androidx.lifecycle.ViewModel  fetchTextbooks androidx.lifecycle.ViewModel  login androidx.lifecycle.ViewModel  register androidx.lifecycle.ViewModel  	Alignment 'com.example.elementarylearningcompanion  Arrangement 'com.example.elementarylearningcompanion  
AuthScreen 'com.example.elementarylearningcompanion  AuthScreenPreview 'com.example.elementarylearningcompanion  Box 'com.example.elementarylearningcompanion  Button 'com.example.elementarylearningcompanion  Card 'com.example.elementarylearningcompanion  
CharacterItem 'com.example.elementarylearningcompanion  CharactersList 'com.example.elementarylearningcompanion  CircularProgressIndicator 'com.example.elementarylearningcompanion  Column 'com.example.elementarylearningcompanion  
Composable 'com.example.elementarylearningcompanion   ElementaryLearningCompanionTheme 'com.example.elementarylearningcompanion  Icon 'com.example.elementarylearningcompanion  
IconButton 'com.example.elementarylearningcompanion  Icons 'com.example.elementarylearningcompanion  Int 'com.example.elementarylearningcompanion  Intent 'com.example.elementarylearningcompanion  LaunchedEffect 'com.example.elementarylearningcompanion  
LazyColumn 'com.example.elementarylearningcompanion  LessonActivity 'com.example.elementarylearningcompanion  
LessonItem 'com.example.elementarylearningcompanion  LessonScreen 'com.example.elementarylearningcompanion  List 'com.example.elementarylearningcompanion  Long 'com.example.elementarylearningcompanion  MainActivity 'com.example.elementarylearningcompanion  
MaterialTheme 'com.example.elementarylearningcompanion  Modifier 'com.example.elementarylearningcompanion  OnboardingActivity 'com.example.elementarylearningcompanion  OnboardingScreen 'com.example.elementarylearningcompanion  OnboardingScreenPreview 'com.example.elementarylearningcompanion  OutlinedTextField 'com.example.elementarylearningcompanion  PasswordVisualTransformation 'com.example.elementarylearningcompanion  Row 'com.example.elementarylearningcompanion  SentenceItem 'com.example.elementarylearningcompanion  
SentencesList 'com.example.elementarylearningcompanion  Spacer 'com.example.elementarylearningcompanion  Surface 'com.example.elementarylearningcompanion  Text 'com.example.elementarylearningcompanion  TextbookItem 'com.example.elementarylearningcompanion  Toast 'com.example.elementarylearningcompanion  Unit 'com.example.elementarylearningcompanion  contains 'com.example.elementarylearningcompanion  	emptyList 'com.example.elementarylearningcompanion  fillMaxSize 'com.example.elementarylearningcompanion  fillMaxWidth 'com.example.elementarylearningcompanion  getValue 'com.example.elementarylearningcompanion  height 'com.example.elementarylearningcompanion  items 'com.example.elementarylearningcompanion  java 'com.example.elementarylearningcompanion  let 'com.example.elementarylearningcompanion  mutableStateOf 'com.example.elementarylearningcompanion  padding 'com.example.elementarylearningcompanion  provideDelegate 'com.example.elementarylearningcompanion  remember 'com.example.elementarylearningcompanion  
setContent 'com.example.elementarylearningcompanion  setValue 'com.example.elementarylearningcompanion  
viewModels 'com.example.elementarylearningcompanion  Bundle 6com.example.elementarylearningcompanion.LessonActivity  ContentViewModel 6com.example.elementarylearningcompanion.LessonActivity   ElementaryLearningCompanionTheme 6com.example.elementarylearningcompanion.LessonActivity  LessonScreen 6com.example.elementarylearningcompanion.LessonActivity  
MaterialTheme 6com.example.elementarylearningcompanion.LessonActivity  Modifier 6com.example.elementarylearningcompanion.LessonActivity  Surface 6com.example.elementarylearningcompanion.LessonActivity  fillMaxSize 6com.example.elementarylearningcompanion.LessonActivity  getFILLMaxSize 6com.example.elementarylearningcompanion.LessonActivity  getFillMaxSize 6com.example.elementarylearningcompanion.LessonActivity  getGETValue 6com.example.elementarylearningcompanion.LessonActivity  getGetValue 6com.example.elementarylearningcompanion.LessonActivity  	getINTENT 6com.example.elementarylearningcompanion.LessonActivity  	getIntent 6com.example.elementarylearningcompanion.LessonActivity  getPROVIDEDelegate 6com.example.elementarylearningcompanion.LessonActivity  getProvideDelegate 6com.example.elementarylearningcompanion.LessonActivity  
getSETContent 6com.example.elementarylearningcompanion.LessonActivity  
getSetContent 6com.example.elementarylearningcompanion.LessonActivity  
getVIEWModels 6com.example.elementarylearningcompanion.LessonActivity  getValue 6com.example.elementarylearningcompanion.LessonActivity  
getViewModels 6com.example.elementarylearningcompanion.LessonActivity  intent 6com.example.elementarylearningcompanion.LessonActivity  provideDelegate 6com.example.elementarylearningcompanion.LessonActivity  
setContent 6com.example.elementarylearningcompanion.LessonActivity  	setIntent 6com.example.elementarylearningcompanion.LessonActivity  	viewModel 6com.example.elementarylearningcompanion.LessonActivity  
viewModels 6com.example.elementarylearningcompanion.LessonActivity  
AuthScreen 4com.example.elementarylearningcompanion.MainActivity  
AuthViewModel 4com.example.elementarylearningcompanion.MainActivity  Bundle 4com.example.elementarylearningcompanion.MainActivity   ElementaryLearningCompanionTheme 4com.example.elementarylearningcompanion.MainActivity  
MaterialTheme 4com.example.elementarylearningcompanion.MainActivity  Modifier 4com.example.elementarylearningcompanion.MainActivity  Surface 4com.example.elementarylearningcompanion.MainActivity  fillMaxSize 4com.example.elementarylearningcompanion.MainActivity  getFILLMaxSize 4com.example.elementarylearningcompanion.MainActivity  getFillMaxSize 4com.example.elementarylearningcompanion.MainActivity  getGETValue 4com.example.elementarylearningcompanion.MainActivity  getGetValue 4com.example.elementarylearningcompanion.MainActivity  getPROVIDEDelegate 4com.example.elementarylearningcompanion.MainActivity  getProvideDelegate 4com.example.elementarylearningcompanion.MainActivity  
getSETContent 4com.example.elementarylearningcompanion.MainActivity  
getSetContent 4com.example.elementarylearningcompanion.MainActivity  
getVIEWModels 4com.example.elementarylearningcompanion.MainActivity  getValue 4com.example.elementarylearningcompanion.MainActivity  
getViewModels 4com.example.elementarylearningcompanion.MainActivity  provideDelegate 4com.example.elementarylearningcompanion.MainActivity  
setContent 4com.example.elementarylearningcompanion.MainActivity  	viewModel 4com.example.elementarylearningcompanion.MainActivity  
viewModels 4com.example.elementarylearningcompanion.MainActivity  Bundle :com.example.elementarylearningcompanion.OnboardingActivity  ContentViewModel :com.example.elementarylearningcompanion.OnboardingActivity   ElementaryLearningCompanionTheme :com.example.elementarylearningcompanion.OnboardingActivity  
MaterialTheme :com.example.elementarylearningcompanion.OnboardingActivity  Modifier :com.example.elementarylearningcompanion.OnboardingActivity  OnboardingScreen :com.example.elementarylearningcompanion.OnboardingActivity  Surface :com.example.elementarylearningcompanion.OnboardingActivity  fillMaxSize :com.example.elementarylearningcompanion.OnboardingActivity  getFILLMaxSize :com.example.elementarylearningcompanion.OnboardingActivity  getFillMaxSize :com.example.elementarylearningcompanion.OnboardingActivity  getGETValue :com.example.elementarylearningcompanion.OnboardingActivity  getGetValue :com.example.elementarylearningcompanion.OnboardingActivity  getPROVIDEDelegate :com.example.elementarylearningcompanion.OnboardingActivity  getProvideDelegate :com.example.elementarylearningcompanion.OnboardingActivity  
getSETContent :com.example.elementarylearningcompanion.OnboardingActivity  
getSetContent :com.example.elementarylearningcompanion.OnboardingActivity  
getVIEWModels :com.example.elementarylearningcompanion.OnboardingActivity  getValue :com.example.elementarylearningcompanion.OnboardingActivity  
getViewModels :com.example.elementarylearningcompanion.OnboardingActivity  provideDelegate :com.example.elementarylearningcompanion.OnboardingActivity  
setContent :com.example.elementarylearningcompanion.OnboardingActivity  	viewModel :com.example.elementarylearningcompanion.OnboardingActivity  
viewModels :com.example.elementarylearningcompanion.OnboardingActivity  ApiResponse +com.example.elementarylearningcompanion.dto  	Character +com.example.elementarylearningcompanion.dto  Lesson +com.example.elementarylearningcompanion.dto  Sentence +com.example.elementarylearningcompanion.dto  TextbookVersion +com.example.elementarylearningcompanion.dto  getLET 7com.example.elementarylearningcompanion.dto.ApiResponse  getLet 7com.example.elementarylearningcompanion.dto.ApiResponse  
getMessage 7com.example.elementarylearningcompanion.dto.ApiResponse  	isSuccess 7com.example.elementarylearningcompanion.dto.ApiResponse  let 7com.example.elementarylearningcompanion.dto.ApiResponse  getCharacterText 5com.example.elementarylearningcompanion.dto.Character  	getPinyin 5com.example.elementarylearningcompanion.dto.Character  equals 2com.example.elementarylearningcompanion.dto.Lesson  getId 2com.example.elementarylearningcompanion.dto.Lesson  getLET 2com.example.elementarylearningcompanion.dto.Lesson  getLessonNumber 2com.example.elementarylearningcompanion.dto.Lesson  getLet 2com.example.elementarylearningcompanion.dto.Lesson  getSentences 2com.example.elementarylearningcompanion.dto.Lesson  getTitle 2com.example.elementarylearningcompanion.dto.Lesson  let 2com.example.elementarylearningcompanion.dto.Lesson  getId 4com.example.elementarylearningcompanion.dto.Sentence  getTextContent 4com.example.elementarylearningcompanion.dto.Sentence  getId ;com.example.elementarylearningcompanion.dto.TextbookVersion  getName ;com.example.elementarylearningcompanion.dto.TextbookVersion  
getSubject ;com.example.elementarylearningcompanion.dto.TextbookVersion  Boolean 0com.example.elementarylearningcompanion.ui.theme  Build 0com.example.elementarylearningcompanion.ui.theme  DarkColorScheme 0com.example.elementarylearningcompanion.ui.theme   ElementaryLearningCompanionTheme 0com.example.elementarylearningcompanion.ui.theme  LightColorScheme 0com.example.elementarylearningcompanion.ui.theme  Pink40 0com.example.elementarylearningcompanion.ui.theme  Pink80 0com.example.elementarylearningcompanion.ui.theme  Purple40 0com.example.elementarylearningcompanion.ui.theme  Purple80 0com.example.elementarylearningcompanion.ui.theme  PurpleGrey40 0com.example.elementarylearningcompanion.ui.theme  PurpleGrey80 0com.example.elementarylearningcompanion.ui.theme  
Typography 0com.example.elementarylearningcompanion.ui.theme  Unit 0com.example.elementarylearningcompanion.ui.theme  WindowCompat 0com.example.elementarylearningcompanion.ui.theme  
AuthViewModel 1com.example.elementarylearningcompanion.viewmodel  ContentViewModel 1com.example.elementarylearningcompanion.viewmodel  
authResult ?com.example.elementarylearningcompanion.viewmodel.AuthViewModel  	isLoading ?com.example.elementarylearningcompanion.viewmodel.AuthViewModel  login ?com.example.elementarylearningcompanion.viewmodel.AuthViewModel  register ?com.example.elementarylearningcompanion.viewmodel.AuthViewModel  
characters Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  error Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  fetchCharactersByLesson Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  fetchLessonDetails Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  fetchLessons Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  fetchTextbooks Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  	isLoading Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  
lessonDetails Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  lessons Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  	textbooks Bcom.example.elementarylearningcompanion.viewmodel.ContentViewModel  	Alignment 	java.lang  Arrangement 	java.lang  
AuthScreen 	java.lang  Build 	java.lang  Button 	java.lang  
CharacterItem 	java.lang  CharactersList 	java.lang  CircularProgressIndicator 	java.lang  Class 	java.lang  Column 	java.lang   ElementaryLearningCompanionTheme 	java.lang  Icon 	java.lang  
IconButton 	java.lang  Icons 	java.lang  Intent 	java.lang  
LazyColumn 	java.lang  LessonActivity 	java.lang  
LessonItem 	java.lang  LessonScreen 	java.lang  
MaterialTheme 	java.lang  Modifier 	java.lang  OnboardingActivity 	java.lang  OnboardingScreen 	java.lang  OutlinedTextField 	java.lang  PasswordVisualTransformation 	java.lang  Row 	java.lang  SentenceItem 	java.lang  
SentencesList 	java.lang  Spacer 	java.lang  Surface 	java.lang  Text 	java.lang  TextbookItem 	java.lang  Toast 	java.lang  WindowCompat 	java.lang  contains 	java.lang  	emptyList 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  getValue 	java.lang  height 	java.lang  java 	java.lang  let 	java.lang  padding 	java.lang  provideDelegate 	java.lang  	Alignment kotlin  Arrangement kotlin  
AuthScreen kotlin  Boolean kotlin  Build kotlin  Button kotlin  
CharacterItem kotlin  CharactersList kotlin  CircularProgressIndicator kotlin  Column kotlin  Double kotlin   ElementaryLearningCompanionTheme kotlin  Float kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Icon kotlin  
IconButton kotlin  Icons kotlin  Int kotlin  Intent kotlin  Lazy kotlin  
LazyColumn kotlin  LessonActivity kotlin  
LessonItem kotlin  LessonScreen kotlin  Long kotlin  
MaterialTheme kotlin  Modifier kotlin  Nothing kotlin  OnboardingActivity kotlin  OnboardingScreen kotlin  OutlinedTextField kotlin  PasswordVisualTransformation kotlin  Row kotlin  SentenceItem kotlin  
SentencesList kotlin  Spacer kotlin  String kotlin  Surface kotlin  Text kotlin  TextbookItem kotlin  Toast kotlin  Unit kotlin  WindowCompat kotlin  contains kotlin  	emptyList kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  getValue kotlin  height kotlin  java kotlin  let kotlin  padding kotlin  provideDelegate kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getLET 
kotlin.Int  getLet 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  getCONTAINS 
kotlin.String  getContains 
kotlin.String  getLET 
kotlin.String  getLet 
kotlin.String  	Alignment kotlin.annotation  Arrangement kotlin.annotation  
AuthScreen kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  
CharacterItem kotlin.annotation  CharactersList kotlin.annotation  CircularProgressIndicator kotlin.annotation  Column kotlin.annotation   ElementaryLearningCompanionTheme kotlin.annotation  Icon kotlin.annotation  
IconButton kotlin.annotation  Icons kotlin.annotation  Intent kotlin.annotation  
LazyColumn kotlin.annotation  LessonActivity kotlin.annotation  
LessonItem kotlin.annotation  LessonScreen kotlin.annotation  
MaterialTheme kotlin.annotation  Modifier kotlin.annotation  OnboardingActivity kotlin.annotation  OnboardingScreen kotlin.annotation  OutlinedTextField kotlin.annotation  PasswordVisualTransformation kotlin.annotation  Row kotlin.annotation  SentenceItem kotlin.annotation  
SentencesList kotlin.annotation  Spacer kotlin.annotation  Surface kotlin.annotation  Text kotlin.annotation  TextbookItem kotlin.annotation  Toast kotlin.annotation  WindowCompat kotlin.annotation  contains kotlin.annotation  	emptyList kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  getValue kotlin.annotation  height kotlin.annotation  java kotlin.annotation  let kotlin.annotation  padding kotlin.annotation  provideDelegate kotlin.annotation  	Alignment kotlin.collections  Arrangement kotlin.collections  
AuthScreen kotlin.collections  Build kotlin.collections  Button kotlin.collections  
CharacterItem kotlin.collections  CharactersList kotlin.collections  CircularProgressIndicator kotlin.collections  Column kotlin.collections   ElementaryLearningCompanionTheme kotlin.collections  Icon kotlin.collections  
IconButton kotlin.collections  Icons kotlin.collections  Intent kotlin.collections  
LazyColumn kotlin.collections  LessonActivity kotlin.collections  
LessonItem kotlin.collections  LessonScreen kotlin.collections  List kotlin.collections  
MaterialTheme kotlin.collections  Modifier kotlin.collections  MutableList kotlin.collections  OnboardingActivity kotlin.collections  OnboardingScreen kotlin.collections  OutlinedTextField kotlin.collections  PasswordVisualTransformation kotlin.collections  Row kotlin.collections  SentenceItem kotlin.collections  
SentencesList kotlin.collections  Spacer kotlin.collections  Surface kotlin.collections  Text kotlin.collections  TextbookItem kotlin.collections  Toast kotlin.collections  WindowCompat kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  getValue kotlin.collections  height kotlin.collections  java kotlin.collections  let kotlin.collections  padding kotlin.collections  provideDelegate kotlin.collections  	Alignment kotlin.comparisons  Arrangement kotlin.comparisons  
AuthScreen kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  
CharacterItem kotlin.comparisons  CharactersList kotlin.comparisons  CircularProgressIndicator kotlin.comparisons  Column kotlin.comparisons   ElementaryLearningCompanionTheme kotlin.comparisons  Icon kotlin.comparisons  
IconButton kotlin.comparisons  Icons kotlin.comparisons  Intent kotlin.comparisons  
LazyColumn kotlin.comparisons  LessonActivity kotlin.comparisons  
LessonItem kotlin.comparisons  LessonScreen kotlin.comparisons  
MaterialTheme kotlin.comparisons  Modifier kotlin.comparisons  OnboardingActivity kotlin.comparisons  OnboardingScreen kotlin.comparisons  OutlinedTextField kotlin.comparisons  PasswordVisualTransformation kotlin.comparisons  Row kotlin.comparisons  SentenceItem kotlin.comparisons  
SentencesList kotlin.comparisons  Spacer kotlin.comparisons  Surface kotlin.comparisons  Text kotlin.comparisons  TextbookItem kotlin.comparisons  Toast kotlin.comparisons  WindowCompat kotlin.comparisons  contains kotlin.comparisons  	emptyList kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  getValue kotlin.comparisons  height kotlin.comparisons  java kotlin.comparisons  let kotlin.comparisons  padding kotlin.comparisons  provideDelegate kotlin.comparisons  SuspendFunction1 kotlin.coroutines  	Alignment 	kotlin.io  Arrangement 	kotlin.io  
AuthScreen 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  
CharacterItem 	kotlin.io  CharactersList 	kotlin.io  CircularProgressIndicator 	kotlin.io  Column 	kotlin.io   ElementaryLearningCompanionTheme 	kotlin.io  Icon 	kotlin.io  
IconButton 	kotlin.io  Icons 	kotlin.io  Intent 	kotlin.io  
LazyColumn 	kotlin.io  LessonActivity 	kotlin.io  
LessonItem 	kotlin.io  LessonScreen 	kotlin.io  
MaterialTheme 	kotlin.io  Modifier 	kotlin.io  OnboardingActivity 	kotlin.io  OnboardingScreen 	kotlin.io  OutlinedTextField 	kotlin.io  PasswordVisualTransformation 	kotlin.io  Row 	kotlin.io  SentenceItem 	kotlin.io  
SentencesList 	kotlin.io  Spacer 	kotlin.io  Surface 	kotlin.io  Text 	kotlin.io  TextbookItem 	kotlin.io  Toast 	kotlin.io  WindowCompat 	kotlin.io  contains 	kotlin.io  	emptyList 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  getValue 	kotlin.io  height 	kotlin.io  java 	kotlin.io  let 	kotlin.io  padding 	kotlin.io  provideDelegate 	kotlin.io  	Alignment 
kotlin.jvm  Arrangement 
kotlin.jvm  
AuthScreen 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  
CharacterItem 
kotlin.jvm  CharactersList 
kotlin.jvm  CircularProgressIndicator 
kotlin.jvm  Column 
kotlin.jvm   ElementaryLearningCompanionTheme 
kotlin.jvm  Icon 
kotlin.jvm  
IconButton 
kotlin.jvm  Icons 
kotlin.jvm  Intent 
kotlin.jvm  
LazyColumn 
kotlin.jvm  LessonActivity 
kotlin.jvm  
LessonItem 
kotlin.jvm  LessonScreen 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  Modifier 
kotlin.jvm  OnboardingActivity 
kotlin.jvm  OnboardingScreen 
kotlin.jvm  OutlinedTextField 
kotlin.jvm  PasswordVisualTransformation 
kotlin.jvm  Row 
kotlin.jvm  SentenceItem 
kotlin.jvm  
SentencesList 
kotlin.jvm  Spacer 
kotlin.jvm  Surface 
kotlin.jvm  Text 
kotlin.jvm  TextbookItem 
kotlin.jvm  Toast 
kotlin.jvm  WindowCompat 
kotlin.jvm  contains 
kotlin.jvm  	emptyList 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  getValue 
kotlin.jvm  height 
kotlin.jvm  java 
kotlin.jvm  let 
kotlin.jvm  padding 
kotlin.jvm  provideDelegate 
kotlin.jvm  	Alignment 
kotlin.ranges  Arrangement 
kotlin.ranges  
AuthScreen 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  
CharacterItem 
kotlin.ranges  CharactersList 
kotlin.ranges  CircularProgressIndicator 
kotlin.ranges  Column 
kotlin.ranges   ElementaryLearningCompanionTheme 
kotlin.ranges  Icon 
kotlin.ranges  
IconButton 
kotlin.ranges  Icons 
kotlin.ranges  Intent 
kotlin.ranges  
LazyColumn 
kotlin.ranges  LessonActivity 
kotlin.ranges  
LessonItem 
kotlin.ranges  LessonScreen 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  Modifier 
kotlin.ranges  OnboardingActivity 
kotlin.ranges  OnboardingScreen 
kotlin.ranges  OutlinedTextField 
kotlin.ranges  PasswordVisualTransformation 
kotlin.ranges  Row 
kotlin.ranges  SentenceItem 
kotlin.ranges  
SentencesList 
kotlin.ranges  Spacer 
kotlin.ranges  Surface 
kotlin.ranges  Text 
kotlin.ranges  TextbookItem 
kotlin.ranges  Toast 
kotlin.ranges  WindowCompat 
kotlin.ranges  contains 
kotlin.ranges  	emptyList 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  getValue 
kotlin.ranges  height 
kotlin.ranges  java 
kotlin.ranges  let 
kotlin.ranges  padding 
kotlin.ranges  provideDelegate 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  	Alignment kotlin.sequences  Arrangement kotlin.sequences  
AuthScreen kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  
CharacterItem kotlin.sequences  CharactersList kotlin.sequences  CircularProgressIndicator kotlin.sequences  Column kotlin.sequences   ElementaryLearningCompanionTheme kotlin.sequences  Icon kotlin.sequences  
IconButton kotlin.sequences  Icons kotlin.sequences  Intent kotlin.sequences  
LazyColumn kotlin.sequences  LessonActivity kotlin.sequences  
LessonItem kotlin.sequences  LessonScreen kotlin.sequences  
MaterialTheme kotlin.sequences  Modifier kotlin.sequences  OnboardingActivity kotlin.sequences  OnboardingScreen kotlin.sequences  OutlinedTextField kotlin.sequences  PasswordVisualTransformation kotlin.sequences  Row kotlin.sequences  SentenceItem kotlin.sequences  
SentencesList kotlin.sequences  Spacer kotlin.sequences  Surface kotlin.sequences  Text kotlin.sequences  TextbookItem kotlin.sequences  Toast kotlin.sequences  WindowCompat kotlin.sequences  contains kotlin.sequences  	emptyList kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  getValue kotlin.sequences  height kotlin.sequences  java kotlin.sequences  let kotlin.sequences  padding kotlin.sequences  provideDelegate kotlin.sequences  	Alignment kotlin.text  Arrangement kotlin.text  
AuthScreen kotlin.text  Build kotlin.text  Button kotlin.text  
CharacterItem kotlin.text  CharactersList kotlin.text  CircularProgressIndicator kotlin.text  Column kotlin.text   ElementaryLearningCompanionTheme kotlin.text  Icon kotlin.text  
IconButton kotlin.text  Icons kotlin.text  Intent kotlin.text  
LazyColumn kotlin.text  LessonActivity kotlin.text  
LessonItem kotlin.text  LessonScreen kotlin.text  
MaterialTheme kotlin.text  Modifier kotlin.text  OnboardingActivity kotlin.text  OnboardingScreen kotlin.text  OutlinedTextField kotlin.text  PasswordVisualTransformation kotlin.text  Row kotlin.text  SentenceItem kotlin.text  
SentencesList kotlin.text  Spacer kotlin.text  Surface kotlin.text  Text kotlin.text  TextbookItem kotlin.text  Toast kotlin.text  WindowCompat kotlin.text  contains kotlin.text  	emptyList kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  getValue kotlin.text  height kotlin.text  java kotlin.text  let kotlin.text  padding kotlin.text  provideDelegate kotlin.text  CoroutineScope kotlinx.coroutines  Intent !kotlinx.coroutines.CoroutineScope  OnboardingActivity !kotlinx.coroutines.CoroutineScope  Toast !kotlinx.coroutines.CoroutineScope  contains !kotlinx.coroutines.CoroutineScope  getCONTAINS !kotlinx.coroutines.CoroutineScope  getContains !kotlinx.coroutines.CoroutineScope  getLET !kotlinx.coroutines.CoroutineScope  getLet !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   