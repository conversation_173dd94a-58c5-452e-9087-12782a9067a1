com\example\elementarylearningcompanion\service\AuthService.class
com\example\elementarylearningcompanion\controller\AuthController.class
com\example\elementarylearningcompanion\service\ContentService.class
com\example\elementarylearningcompanion\model\Sentence.class
com\example\elementarylearningcompanion\config\SecurityConfig.class
com\example\elementarylearningcompanion\model\Student.class
com\example\elementarylearningcompanion\repository\ParentRepository.class
com\example\elementarylearningcompanion\repository\CharacterRepository.class
com\example\elementarylearningcompanion\model\Parent.class
com\example\elementarylearningcompanion\model\Lesson.class
com\example\elementarylearningcompanion\model\TextbookVersion.class
com\example\elementarylearningcompanion\dto\ApiResponse.class
com\example\elementarylearningcompanion\dto\AuthRequest.class
com\example\elementarylearningcompanion\model\Character.class
com\example\elementarylearningcompanion\repository\SentenceRepository.class
com\example\elementarylearningcompanion\controller\ContentController.class
com\example\elementarylearningcompanion\repository\StudentRepository.class
com\example\elementarylearningcompanion\repository\LessonRepository.class
com\example\elementarylearningcompanion\ElementaryLearningCompanionApplication.class
com\example\elementarylearningcompanion\model\StudentParentLink.class
com\example\elementarylearningcompanion\repository\TextbookVersionRepository.class
