package com.example.elementarylearningcompanion.repository;

import com.example.elementarylearningcompanion.model.Character;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CharacterRepository extends JpaRepository<Character, Long> {

    List<Character> findByLessonId(Long lessonId);

}
