<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2017 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->

<typesystem package="PySide6.Qt3DInput" doc-package="PySide6.Qt3D" doc-mode="flat"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
    <load-typesystem name="typesystem_3dcore.xml" generate="no"/>
    <namespace-type name="Qt3DInput">
        <object-type name="QAbstractActionInput"/>
        <object-type name="QAbstractAxisInput"/>
        <object-type name="QAbstractPhysicalDevice"/>
        <object-type name="QAction">
            <!-- Disambiguate from QtGui/qaction.h -->
            <include file-name="Qt3DInput/qaction.h" location="global"/>
        </object-type>
        <object-type name="QActionInput"/>
        <object-type name="QAnalogAxisInput"/>
        <object-type name="QAxis"/>
        <object-type name="QAxisAccumulator">
            <enum-type name="SourceAxisType"/>
        </object-type>
        <object-type name="QAxisSetting"/>
        <object-type name="QButtonAxisInput"/>
        <object-type name="QInputAspect"/>
        <object-type name="QInputChord"/>
        <!-- On windows this raises the following error:
        type 'Qt3DInput::QInputDeviceIntegration' is specified in typesystem, but not defined.
        This could potentially lead to compilation errors.
        <object-type name="QInputDeviceIntegration"/>
        -->
        <object-type name="QInputSequence"/>
        <object-type name="QInputSettings"/>
        <object-type name="QKeyboardDevice"/>
        <object-type name="QKeyboardHandler"/>
        <object-type name="QKeyEvent"/>
        <object-type name="QLogicalDevice"/>
        <object-type name="QMouseDevice">
            <enum-type name="Axis"/>
        </object-type>
        <object-type name="QMouseEvent">
            <enum-type name="Buttons"/>
            <enum-type name="Modifiers"/>
        </object-type>
        <object-type name="QWheelEvent">
            <enum-type name="Buttons"/>
            <enum-type name="Modifiers"/>
        </object-type>
        <object-type name="QMouseHandler"/>
        <!-- On windows this raise the following error:
        qt3dinput_module_wrapper.cpp.obj : error LNK2019:
        unresolved external symbol "void __cdecl init_Qt3DInput_QPhysicalDeviceCreatedChangeBase(struct _object *)"
        (?init_Qt3DInput_QPhysicalDeviceCreatedChangeBase@@YAXPAU_object@@@Z) referenced in function _PyInit_Qt3DInput
        <object-type name="QPhysicalDeviceCreatedChangeBase"/>
        -->
    </namespace-type>
</typesystem>
