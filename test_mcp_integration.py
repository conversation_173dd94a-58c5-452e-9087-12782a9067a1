#!/usr/bin/env python3
"""
Interactive Feedback MCP 集成测试脚本

这个脚本用于测试MCP服务器是否正常工作，以及Augment是否能够正确调用interactive_feedback工具。
"""

import json
import subprocess
import sys
import time
import os
from pathlib import Path

def test_mcp_server():
    """测试MCP服务器基本功能"""
    print("测试MCP服务器...")
    
    mcp_dir = Path("f:/study/interactive-feedback-mcp")
    if not mcp_dir.exists():
        print(f"❌ MCP目录不存在: {mcp_dir}")
        return False
    
    try:
        # 测试服务器模块导入
        result = subprocess.run([
            "uv", "run", "python", "-c", 
            "import server; print('✅ Server module loaded')"
        ], 
        cwd=mcp_dir, 
        capture_output=True, 
        text=True, 
        timeout=30
        )
        
        if result.returncode == 0:
            print("✅ MCP服务器模块加载成功")
            return True
        else:
            print(f"❌ MCP服务器模块加载失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试MCP服务器时出错: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("📦 测试依赖包...")
    
    mcp_dir = Path("f:/study/interactive-feedback-mcp")
    
    try:
        result = subprocess.run([
            "uv", "run", "python", "-c",
            "import fastmcp, psutil, PySide6; print('✅ All dependencies OK')"
        ],
        cwd=mcp_dir,
        capture_output=True,
        text=True,
        timeout=30
        )
        
        if result.returncode == 0:
            print("✅ 所有依赖包正常")
            return True
        else:
            print(f"❌ 依赖包检查失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试依赖包时出错: {e}")
        return False

def test_mcp_dev_mode():
    """测试MCP开发模式"""
    print("🌐 测试MCP开发模式...")
    
    mcp_dir = Path("f:/study/interactive-feedback-mcp")
    
    try:
        # 启动开发模式服务器（非阻塞）
        process = subprocess.Popen([
            "uv", "run", "fastmcp", "dev", "server.py"
        ],
        cwd=mcp_dir,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True
        )
        
        # 等待服务器启动
        time.sleep(5)
        
        if process.poll() is None:
            print("✅ MCP开发模式服务器启动成功")
            
            # 读取输出查找URL
            try:
                stdout, _ = process.communicate(timeout=2)
                if "localhost" in stdout or "127.0.0.1" in stdout:
                    print("✅ 检测到Web界面URL")
            except subprocess.TimeoutExpired:
                pass
            
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ MCP开发模式启动失败")
            print(f"输出: {stdout}")
            print(f"错误: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试MCP开发模式时出错: {e}")
        if 'process' in locals():
            try:
                process.terminate()
            except:
                pass
        return False

def validate_config():
    """验证配置文件"""
    print("📋 验证配置文件...")
    
    config_file = Path("augment-mcp-config.json")
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必要字段
        if "mcpServers" not in config:
            print("❌ 配置文件缺少mcpServers字段")
            return False
        
        if "interactive-feedback-mcp" not in config["mcpServers"]:
            print("❌ 配置文件缺少interactive-feedback-mcp服务器配置")
            return False
        
        server_config = config["mcpServers"]["interactive-feedback-mcp"]
        required_fields = ["command", "args"]
        
        for field in required_fields:
            if field not in server_config:
                print(f"❌ 服务器配置缺少必要字段: {field}")
                return False
        
        # 检查路径
        if "--directory" in server_config["args"]:
            dir_index = server_config["args"].index("--directory") + 1
            if dir_index < len(server_config["args"]):
                mcp_path = server_config["args"][dir_index]
                if not Path(mcp_path).exists():
                    print(f"❌ MCP目录路径不存在: {mcp_path}")
                    return False
        
        print("✅ 配置文件验证通过")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证配置文件时出错: {e}")
        return False

def generate_test_report():
    """生成测试报告"""
    print("\n" + "="*60)
    print("📊 Interactive Feedback MCP 集成测试报告")
    print("="*60)
    
    tests = [
        ("MCP服务器基本功能", test_mcp_server),
        ("依赖包检查", test_dependencies),
        ("配置文件验证", validate_config),
        ("MCP开发模式", test_mcp_dev_mode),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("📋 测试结果汇总:")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！Interactive Feedback MCP已准备好集成到Augment中。")
        print("\n📝 下一步:")
        print("1. 将 augment-mcp-config.json 的内容添加到Augment的MCP配置中")
        print("2. 重启Augment以加载新配置")
        print("3. 在Augment中测试 interactive_feedback 工具")
        print("4. 参考 AUGMENT_INTEGRATION_GUIDE.md 了解详细使用方法")
    else:
        print(f"\n⚠️  有 {total - passed} 个测试失败，请检查相关问题后重新测试。")
        print("\n🔧 故障排除建议:")
        print("1. 确认Interactive Feedback MCP已正确安装")
        print("2. 检查Python和uv环境")
        print("3. 验证所有依赖包已安装")
        print("4. 查看错误信息并修复相关问题")

if __name__ == "__main__":
    print("开始Interactive Feedback MCP集成测试...")
    generate_test_report()
