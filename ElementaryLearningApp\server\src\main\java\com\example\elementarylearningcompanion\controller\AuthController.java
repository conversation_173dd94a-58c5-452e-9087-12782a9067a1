package com.example.elementarylearningcompanion.controller;

import com.example.elementarylearningcompanion.dto.AuthRequest;
import com.example.elementarylearningcompanion.dto.ApiResponse;
import com.example.elementarylearningcompanion.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/auth")
public class AuthController {

    @Autowired
    private AuthService authService;

    @PostMapping("/register")
    public ResponseEntity<ApiResponse> registerParent(@RequestBody AuthRequest authRequest) {
        try {
            authService.registerParent(authRequest.getPhoneNumber(), authRequest.getPassword());
            return ResponseEntity.ok(new ApiResponse(true, "Registration successful."));
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(new ApiResponse(false, e.getMessage()));
        }
    }

    @PostMapping("/login")
    public ResponseEntity<ApiResponse> login(@RequestBody AuthRequest authRequest) {
        boolean isAuthenticated = authService.login(authRequest.getPhoneNumber(), authRequest.getPassword());
        if (isAuthenticated) {
            // In a real app, you'd return a JWT token here.
            return ResponseEntity.ok(new ApiResponse(true, "Login successful."));
        } else {
            return ResponseEntity.status(401).body(new ApiResponse(false, "Invalid credentials."));
        }
    }
}
