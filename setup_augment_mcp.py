#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment MCP自动配置脚本
自动查找并配置Augment的MCP设置
"""

import json
import os
import shutil
from pathlib import Path
from typing import Dict, Any, Optional

class AugmentMCPConfigurator:
    """Augment MCP配置器"""
    
    def __init__(self):
        self.config_data = {
            "mcpServers": {
                "interactive-feedback-mcp": {
                    "command": "uv",
                    "args": [
                        "--directory",
                        "f:\\study\\interactive-feedback-mcp",
                        "run",
                        "server.py"
                    ],
                    "timeout": 600,
                    "autoApprove": [
                        "interactive_feedback"
                    ],
                    "description": "Interactive User Feedback MCP - 允许AI主动请求用户反馈的工具",
                    "env": {
                        "PYTHONUNBUFFERED": "1"
                    }
                }
            }
        }
    
    def find_augment_config_paths(self) -> list[Path]:
        """查找可能的Augment配置文件路径"""
        
        possible_paths = []
        
        # Windows常见路径
        if os.name == 'nt':
            appdata = os.environ.get('APPDATA', '')
            userprofile = os.environ.get('USERPROFILE', '')
            
            possible_paths.extend([
                Path(appdata) / 'Augment' / 'mcp.json',
                Path(appdata) / 'Augment' / 'config' / 'mcp.json',
                Path(userprofile) / '.augment' / 'mcp.json',
                Path(userprofile) / '.config' / 'augment' / 'mcp.json',
                Path(userprofile) / 'AppData' / 'Local' / 'Augment' / 'mcp.json',
                Path(userprofile) / 'AppData' / 'Roaming' / 'Augment' / 'mcp.json',
            ])
        
        # 通用路径
        home = Path.home()
        possible_paths.extend([
            home / '.augment' / 'mcp.json',
            home / '.config' / 'augment' / 'mcp.json',
            Path.cwd() / 'mcp.json',
            Path.cwd() / 'augment-mcp.json',
        ])
        
        return possible_paths
    
    def find_existing_config(self) -> Optional[Path]:
        """查找现有的配置文件"""
        
        for path in self.find_augment_config_paths():
            if path.exists():
                print(f"找到现有配置文件: {path}")
                return path
        
        return None
    
    def backup_config(self, config_path: Path) -> Path:
        """备份现有配置文件"""
        
        backup_path = config_path.with_suffix('.json.backup')
        shutil.copy2(config_path, backup_path)
        print(f"已备份配置文件到: {backup_path}")
        return backup_path
    
    def merge_config(self, existing_config: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置"""
        
        if "mcpServers" not in existing_config:
            existing_config["mcpServers"] = {}
        
        # 添加我们的配置
        existing_config["mcpServers"]["interactive-feedback-mcp"] = \
            self.config_data["mcpServers"]["interactive-feedback-mcp"]
        
        return existing_config
    
    def create_new_config_file(self, target_path: Path) -> bool:
        """创建新的配置文件"""
        
        try:
            # 确保目录存在
            target_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入配置
            with open(target_path, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
            
            print(f"已创建新配置文件: {target_path}")
            return True
            
        except Exception as e:
            print(f"创建配置文件失败: {e}")
            return False
    
    def update_existing_config(self, config_path: Path) -> bool:
        """更新现有配置文件"""
        
        try:
            # 备份原文件
            self.backup_config(config_path)
            
            # 读取现有配置
            with open(config_path, 'r', encoding='utf-8') as f:
                existing_config = json.load(f)
            
            # 合并配置
            merged_config = self.merge_config(existing_config)
            
            # 写入更新后的配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(merged_config, f, indent=2, ensure_ascii=False)
            
            print(f"已更新配置文件: {config_path}")
            return True
            
        except Exception as e:
            print(f"更新配置文件失败: {e}")
            return False
    
    def suggest_manual_config(self):
        """提供手动配置建议"""
        
        print("\n" + "="*60)
        print("手动配置指南")
        print("="*60)
        print("如果自动配置失败，请手动将以下配置添加到Augment的MCP设置中：")
        print()
        print("配置内容：")
        print(json.dumps(self.config_data, indent=2, ensure_ascii=False))
        print()
        print("可能的配置位置：")
        for path in self.find_augment_config_paths():
            print(f"  - {path}")
    
    def run(self):
        """运行配置器"""
        
        print("Augment MCP自动配置器")
        print("="*40)
        
        # 检查Interactive Feedback MCP是否存在
        mcp_path = Path("f:/study/interactive-feedback-mcp")
        if not mcp_path.exists():
            print(f"错误: Interactive Feedback MCP目录不存在: {mcp_path}")
            print("请先安装Interactive Feedback MCP")
            return False
        
        print("✓ Interactive Feedback MCP目录存在")
        
        # 查找现有配置
        existing_config_path = self.find_existing_config()
        
        if existing_config_path:
            # 更新现有配置
            print(f"发现现有配置文件: {existing_config_path}")
            success = self.update_existing_config(existing_config_path)
        else:
            # 创建新配置文件
            print("未找到现有配置文件，将创建新的配置文件")
            
            # 尝试几个常见位置
            target_paths = [
                Path.home() / '.augment' / 'mcp.json',
                Path(os.environ.get('APPDATA', '')) / 'Augment' / 'mcp.json',
                Path.cwd() / 'augment-mcp-config.json'
            ]
            
            success = False
            for target_path in target_paths:
                if target_path.parent.exists() or target_path.parent == Path.cwd():
                    success = self.create_new_config_file(target_path)
                    if success:
                        break
            
            if not success:
                # 在当前目录创建
                success = self.create_new_config_file(Path.cwd() / 'augment-mcp-config.json')
        
        if success:
            print("\n" + "="*60)
            print("配置完成！")
            print("="*60)
            print("下一步：")
            print("1. 重启Augment以加载新配置")
            print("2. 在Augment中测试interactive_feedback工具")
            print("3. 享受人机交互循环的AI体验！")
            print()
            print("测试命令：")
            print("请使用interactive_feedback工具，告诉我你已经准备好了。")
        else:
            print("\n自动配置失败")
            self.suggest_manual_config()
        
        return success

def main():
    """主函数"""
    configurator = AugmentMCPConfigurator()
    configurator.run()

if __name__ == "__main__":
    main()
