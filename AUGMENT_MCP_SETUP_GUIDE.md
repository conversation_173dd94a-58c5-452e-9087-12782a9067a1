# 🚀 Augment MCP配置详细指南

## 📋 配置概述

您需要将Interactive Feedback MCP添加到Augment的MCP服务器配置中。有两种主要方法：

## 方法一：通过Augment界面配置（推荐）

### 步骤1: 打开Augment设置
1. 启动Augment
2. 查找设置/配置选项（通常在菜单栏或设置图标）
3. 寻找"MCP"、"MCP Servers"、"Extensions"或"Plugins"相关选项

### 步骤2: 添加MCP服务器
在MCP配置界面中，添加以下信息：

**服务器名称**: `interactive-feedback-mcp`

**命令**: `uv`

**参数**:
```
--directory
f:\study\interactive-feedback-mcp
run
server.py
```

**超时时间**: `600`

**自动批准工具**: `interactive_feedback`

**环境变量**:
- `PYTHONUNBUFFERED`: `1`

## 方法二：通过配置文件配置

### 步骤1: 找到Augment配置文件
Augment的MCP配置文件可能位于：
- `%APPDATA%\Augment\mcp.json`
- `%USERPROFILE%\.augment\mcp.json`
- `%USERPROFILE%\.config\augment\mcp.json`
- Augment安装目录下的配置文件

### 步骤2: 编辑配置文件
将以下配置添加到现有的`mcpServers`部分：

```json
{
  "mcpServers": {
    "interactive-feedback-mcp": {
      "command": "uv",
      "args": [
        "--directory",
        "f:\\study\\interactive-feedback-mcp",
        "run",
        "server.py"
      ],
      "timeout": 600,
      "autoApprove": [
        "interactive_feedback"
      ],
      "description": "Interactive User Feedback MCP - 允许AI主动请求用户反馈的工具",
      "env": {
        "PYTHONUNBUFFERED": "1"
      }
    }
  }
}
```

### 如果配置文件不存在
创建新的配置文件，内容如上所示。

### 如果已有其他MCP服务器
将我们的配置添加到现有的`mcpServers`对象中：

```json
{
  "mcpServers": {
    "existing-server": {
      "command": "...",
      "args": ["..."]
    },
    "interactive-feedback-mcp": {
      "command": "uv",
      "args": [
        "--directory",
        "f:\\study\\interactive-feedback-mcp",
        "run",
        "server.py"
      ],
      "timeout": 600,
      "autoApprove": [
        "interactive_feedback"
      ],
      "description": "Interactive User Feedback MCP - 允许AI主动请求用户反馈的工具",
      "env": {
        "PYTHONUNBUFFERED": "1"
      }
    }
  }
}
```

## 🔍 验证配置

### 步骤1: 重启Augment
保存配置后，完全关闭并重新启动Augment。

### 步骤2: 检查MCP连接状态
在Augment中查找MCP状态指示器，确认：
- Interactive Feedback MCP显示为"已连接"或绿色状态
- 没有连接错误信息

### 步骤3: 测试工具
在Augment中尝试以下命令：

```
请使用interactive_feedback工具，告诉我你已经准备好接收我的反馈了。
```

## 🛠️ 故障排除

### 问题1: MCP服务器无法启动
**症状**: Augment显示连接失败或红色状态

**解决方案**:
1. 检查路径是否正确：`f:\study\interactive-feedback-mcp`
2. 确认uv命令可用：在命令行运行 `uv --version`
3. 手动测试MCP服务器：
   ```bash
   cd f:\study\interactive-feedback-mcp
   uv run server.py
   ```

### 问题2: 工具调用失败
**症状**: Augment找不到interactive_feedback工具

**解决方案**:
1. 确认`autoApprove`配置包含`interactive_feedback`
2. 检查超时设置是否合理（建议600秒）
3. 重启Augment重新加载配置

### 问题3: 配置文件格式错误
**症状**: Augment无法解析配置文件

**解决方案**:
1. 使用JSON验证器检查语法
2. 确保所有引号和括号匹配
3. 检查路径中的反斜杠是否正确转义（`\\`）

## 📝 配置模板

### 完整配置模板
```json
{
  "mcpServers": {
    "interactive-feedback-mcp": {
      "command": "uv",
      "args": [
        "--directory",
        "f:\\study\\interactive-feedback-mcp",
        "run",
        "server.py"
      ],
      "timeout": 600,
      "autoApprove": [
        "interactive_feedback"
      ],
      "description": "Interactive User Feedback MCP - 允许AI主动请求用户反馈的工具",
      "env": {
        "PYTHONUNBUFFERED": "1"
      }
    }
  }
}
```

### 最小配置模板
```json
{
  "mcpServers": {
    "interactive-feedback-mcp": {
      "command": "uv",
      "args": [
        "--directory",
        "f:\\study\\interactive-feedback-mcp",
        "run",
        "server.py"
      ],
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

## 🎯 下一步

配置完成后：

1. **重启Augment**
2. **测试工具功能**
3. **开始享受人机交互循环的AI体验**

## 📞 需要帮助？

如果遇到配置问题：
1. 检查本指南的故障排除部分
2. 确认Interactive Feedback MCP安装正确
3. 验证Augment版本支持MCP协议

---

**配置成功后，您的Augment Agent将具备主动请求用户反馈的能力！** 🎉
