<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtSql"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
  <load-typesystem name="typesystem_widgets.xml" generate="no"/>

  <rejection class="QSqlDriverCreator"/>
  <rejection class="QSqlDriverPlugin"/>

  <namespace-type name="QSql">
    <enum-type name="Location"/>
    <enum-type name="ParamTypeFlag" flags="ParamType"/>
    <enum-type name="TableType"/>
    <enum-type name="NumericalPrecisionPolicy"/>
    <extra-includes>
        <include file-name="QtSql/qtsqlglobal.h" location="global"/>
    </extra-includes>
  </namespace-type>

  <value-type name="QSqlDatabase">
    <extra-includes>
        <include file-name="QSqlQuery" location="global"/>
        <include file-name="QSqlError" location="global"/>
        <include file-name="QSqlIndex" location="global"/>
        <include file-name="QSqlRecord" location="global"/>
        <include file-name="QStringList" location="global"/>
        <include file-name="QSize" location="global"/>
    </extra-includes>
    <modify-function signature="exec(QString)const" allow-thread="yes"/>
    <add-function signature="exec_(QString @query@ = QString())const" return-type="QSqlQuery">
        <inject-code file="../glue/qtsql.cpp" snippet="qsqldatabase-exec"/>
    </add-function>
    <modify-function signature="open()" allow-thread="yes"/>
    <modify-function signature="open(const QString&amp;, const QString&amp;)" allow-thread="yes"/>
    <modify-function signature="commit()" allow-thread="yes"/>
    <modify-function signature="rollback()" allow-thread="yes"/>
    <modify-function signature="transaction()" allow-thread="yes"/>
    <modify-function signature="registerSqlDriver(const QString&amp;,QSqlDriverCreatorBase*)">
      <modify-argument index="2">
        <define-ownership owner="c++"/>
      </modify-argument>
    </modify-function>
  </value-type>

  <value-type name="QSqlQuery">
    <enum-type name="BatchExecutionMode"/>
    <extra-includes>
        <include file-name="QSqlError" location="global"/>
        <include file-name="QSqlRecord" location="global"/>
        <include file-name="QStringList" location="global"/>
        <include file-name="QSize" location="global"/>
    </extra-includes>
    <!-- exec() -->
    <modify-function signature="exec()" allow-thread="yes"/>
    <add-function signature="exec_()" return-type="bool">
        <inject-code file="../glue/qtsql.cpp" snippet="simple-exec"/>
    </add-function>
    <!-- exec(QString) -->
    <modify-function signature="exec(const QString&amp;)" allow-thread="yes"/>
    <add-function signature="exec_(const QString&amp;)" return-type="bool">
        <inject-code file="../glue/qtsql.cpp" snippet="qsqlquery-exec"/>
    </add-function>
    <modify-function signature="prepare(const QString&amp;)" allow-thread="yes"/>
    <modify-function signature="clear()" allow-thread="yes"/>
    <modify-function signature="last()" allow-thread="yes"/>
    <modify-function signature="first()" allow-thread="yes"/>
    <modify-function signature="previous()" allow-thread="yes"/>
    <modify-function signature="next()" allow-thread="yes"/>
    <modify-function signature="seek(int,bool)" allow-thread="yes"/>
  </value-type>

  <value-type name="QSqlRecord">
    <extra-includes>
        <include file-name="QSqlField" location="global"/>
    </extra-includes>
  </value-type>

  <value-type name="QSqlError">
    <enum-type name="ErrorType"/>
  </value-type>

  <value-type name="QSqlIndex"/>

  <value-type name="QSqlRelation"/>

  <object-type name="QSqlRelationalDelegate"/>

  <value-type name="QSqlField">
      <enum-type name="RequiredStatus"/>
  </value-type>

  <object-type name="QSqlDriver">
    <enum-type name="DbmsType"/>
    <enum-type name="DriverFeature"/>
    <enum-type name="IdentifierType"/>
    <enum-type name="NotificationSource"/>
    <enum-type name="StatementType"/>
    <extra-includes>
        <include file-name="QSqlQuery" location="global"/>
        <include file-name="QSqlError" location="global"/>
        <include file-name="QSqlIndex" location="global"/>
        <include file-name="QSqlRecord" location="global"/>
        <include file-name="QStringList" location="global"/>
        <include file-name="QSize" location="global"/>
    </extra-includes>
    <modify-function signature="beginTransaction()" allow-thread="yes"/>
    <modify-function signature="commitTransaction()" allow-thread="yes"/>
    <modify-function signature="rollbackTransaction()" allow-thread="yes"/>
    <modify-function signature="open(const QString&amp;,const QString&amp;,const QString&amp;,const QString&amp;,int,const QString&amp;)" allow-thread="yes"/>
    <!-- ### This is too low level for Python, and pointer would be useless for the Python programmer -->
    <modify-function signature="handle()const" remove="all"/>
    <!-- ### -->
  </object-type>

  <object-type name="QSqlQueryModel">
    <extra-includes>
        <include file-name="QSqlError" location="global"/>
        <include file-name="QSqlQuery" location="global"/>
        <include file-name="QSqlRecord" location="global"/>
        <include file-name="QStringList" location="global"/>
        <include file-name="QSize" location="global"/>
    </extra-includes>
    <!-- FIXME: PYSIDE7: Handle setQuery(QSqlQuery&&) in some way?
         QTBUG-91766/PYSIDE-2394. allow-thread for PYSIDE-1931 -->
    <modify-function signature="setQuery(QSqlQuery)" allow-thread="yes"
                     deprecated="false"/>
    <modify-function signature="setQuery(QString,QSqlDatabase)" allow-thread="yes"/>
    <!-- FIXME: PYSIDE7: Probably needs a fix, make QSqlQuery an object type?
         QTBUG-105048/PYSIDE-2300 -->
    <declare-function signature="query()" return-type="QSqlQuery"/>
  </object-type>
  <object-type name="QSqlRelationalTableModel">
    <enum-type name="JoinMode"/>
    <extra-includes>
        <include file-name="QStringList" location="global"/>
        <include file-name="QSize" location="global"/>
    </extra-includes>
    <modify-function signature="select()" allow-thread="yes"/> <!-- PYSIDE-1931 -->
  </object-type>
  <object-type name="QSqlResult">
      <enum-type name="BindingSyntax"/>
      <enum-type name="VirtualHookOperation"/>
      <extra-includes>
        <include file-name="QSqlError" location="global"/>
        <include file-name="QSqlQuery" location="global"/>
        <include file-name="QSqlRecord" location="global"/>
        <include file-name="QStringList" location="global"/>
        <include file-name="QSize" location="global"/>
      </extra-includes>
      <!-- ### This isn't part of Qt public API -->
      <modify-function signature="virtual_hook(int,void*)" remove="all"/>
      <!-- ### -->
      <modify-function signature="exec()" allow-thread="yes"/>
      <add-function signature="exec_()" return-type="bool">
          <inject-code file="../glue/qtsql.cpp" snippet="qsqlresult-exec"/>
      </add-function>
      <modify-function signature="fetchLast()" allow-thread="yes"/>
      <modify-function signature="fetchFirst()" allow-thread="yes"/>
      <modify-function signature="fetchNext()" allow-thread="yes"/>
      <modify-function signature="fetchPrevious()" allow-thread="yes"/>
      <modify-function signature="fetch(int)" allow-thread="yes"/>
      <modify-function signature="prepare(QString)" allow-thread="yes"/>
  </object-type>
  <object-type name="QSqlTableModel">
    <enum-type name="EditStrategy"/>
    <extra-includes>
        <include file-name="QSqlIndex" location="global"/>
        <include file-name="QStringList" location="global"/>
        <include file-name="QSize" location="global"/>
    </extra-includes>
    <modify-function signature="select()" allow-thread="yes"/> <!-- PYSIDE-1931 -->
  </object-type>
  <object-type name="QSqlDriverCreatorBase">
    <extra-includes>
        <include file-name="QSqlDriver" location="global"/>
    </extra-includes>
  </object-type>

</typesystem>
