@echo off
REM Interactive Feedback MCP 服务器启动脚本
REM 用于手动启动MCP服务器进行测试

echo ========================================
echo Interactive Feedback MCP 服务器启动
echo ========================================

cd /d "f:\study\interactive-feedback-mcp"

echo 当前目录: %CD%
echo.

echo 检查uv环境...
uv --version
if %ERRORLEVEL% neq 0 (
    echo 错误: uv未安装或不在PATH中
    pause
    exit /b 1
)

echo.
echo 启动MCP服务器...
echo 按 Ctrl+C 停止服务器
echo.

uv run server.py

echo.
echo 服务器已停止
pause
