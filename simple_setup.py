#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Augment MCP配置脚本
"""

import json
import os
from pathlib import Path

def create_config():
    """创建MCP配置"""
    
    config = {
        "mcpServers": {
            "interactive-feedback-mcp": {
                "command": "uv",
                "args": [
                    "--directory",
                    "f:\\study\\interactive-feedback-mcp",
                    "run",
                    "server.py"
                ],
                "timeout": 600,
                "autoApprove": [
                    "interactive_feedback"
                ],
                "description": "Interactive User Feedback MCP",
                "env": {
                    "PYTHONUNBUFFERED": "1"
                }
            }
        }
    }
    
    return config

def main():
    print("=== Augment MCP 配置器 ===")
    
    # 检查MCP目录
    mcp_path = Path("f:/study/interactive-feedback-mcp")
    if not mcp_path.exists():
        print("错误: Interactive Feedback MCP目录不存在")
        return
    
    print("Interactive Feedback MCP目录存在")
    
    # 创建配置
    config = create_config()
    
    # 保存到当前目录
    output_file = Path("augment-mcp-final-config.json")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"配置文件已创建: {output_file}")
    print()
    print("=== 下一步操作 ===")
    print("1. 将配置文件内容复制到Augment的MCP设置中")
    print("2. 或者将此文件导入到Augment")
    print("3. 重启Augment")
    print("4. 测试interactive_feedback工具")
    print()
    print("配置内容:")
    print(json.dumps(config, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    main()
