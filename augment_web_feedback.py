"""
Augment Web Interactive Feedback Tool
基于Web的轻量级交互反馈工具，专为Augment Agent设计

这个工具提供一个简单的Web界面，让用户可以：
1. 查看AI的工作摘要
2. 执行验证命令
3. 提供文字反馈
4. 快速响应AI的请求
"""

import os
import json
import asyncio
import subprocess
import threading
import webbrowser
from typing import Dict, Any, Optional
from pathlib import Path
import tempfile
import time

try:
    from fastapi import FastAPI, Request, Form
    from fastapi.responses import HTMLResponse, JSONResponse
    from fastapi.staticfiles import StaticFiles
    import uvicorn
    WEB_AVAILABLE = True
except ImportError:
    WEB_AVAILABLE = False
    print("警告: FastAPI未安装，将使用命令行模式")


class AugmentWebFeedback:
    """基于Web的Augment交互反馈工具"""
    
    def __init__(self):
        self.app = None
        self.server_process = None
        self.feedback_result = None
        self.feedback_received = False
        
    def request_feedback(
        self,
        project_directory: str = ".",
        summary: str = "我已完成了您请求的任务，请查看并提供反馈。",
        timeout: int = 600
    ) -> Dict[str, Any]:
        """请求用户反馈"""
        
        if not WEB_AVAILABLE:
            return self._fallback_feedback(project_directory, summary)
            
        try:
            # 启动Web服务器
            port = self._find_free_port()
            self._setup_web_app(project_directory, summary)
            
            # 在后台启动服务器
            server_thread = threading.Thread(
                target=self._run_server,
                args=(port,),
                daemon=True
            )
            server_thread.start()
            
            # 等待服务器启动
            time.sleep(2)
            
            # 打开浏览器
            feedback_url = f"http://localhost:{port}"
            webbrowser.open(feedback_url)
            
            print(f"🌐 反馈界面已在浏览器中打开: {feedback_url}")
            print("⏳ 等待用户反馈...")
            
            # 等待反馈或超时
            start_time = time.time()
            while not self.feedback_received and (time.time() - start_time) < timeout:
                time.sleep(0.5)
                
            if self.feedback_received:
                return {
                    "success": True,
                    "feedback": self.feedback_result.get("feedback", ""),
                    "logs": self.feedback_result.get("logs", ""),
                    "command": self.feedback_result.get("command", "")
                }
            else:
                return {
                    "success": False,
                    "error": "超时或用户取消",
                    "feedback": "",
                    "logs": ""
                }
                
        except Exception as e:
            print(f"Web反馈启动失败: {e}")
            return self._fallback_feedback(project_directory, summary)
    
    def _setup_web_app(self, project_directory: str, summary: str):
        """设置Web应用"""
        
        self.app = FastAPI(title="Augment Interactive Feedback")
        self.project_directory = project_directory
        self.summary = summary
        
        @self.app.get("/", response_class=HTMLResponse)
        async def feedback_page():
            return self._get_feedback_html()
            
        @self.app.post("/execute")
        async def execute_command(command: str = Form(...)):
            """执行命令"""
            try:
                result = subprocess.run(
                    command,
                    shell=True,
                    cwd=self.project_directory,
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                output = f"$ {command}\n"
                if result.stdout:
                    output += f"输出:\n{result.stdout}\n"
                if result.stderr:
                    output += f"错误:\n{result.stderr}\n"
                output += f"返回码: {result.returncode}"
                
                return JSONResponse({
                    "success": True,
                    "output": output,
                    "returncode": result.returncode
                })
                
            except subprocess.TimeoutExpired:
                return JSONResponse({
                    "success": False,
                    "output": "命令执行超时（30秒）",
                    "returncode": -1
                })
            except Exception as e:
                return JSONResponse({
                    "success": False,
                    "output": f"执行失败: {str(e)}",
                    "returncode": -1
                })
        
        @self.app.post("/submit")
        async def submit_feedback(
            feedback: str = Form(...),
            logs: str = Form(""),
            command: str = Form("")
        ):
            """提交反馈"""
            self.feedback_result = {
                "feedback": feedback,
                "logs": logs,
                "command": command
            }
            self.feedback_received = True
            
            return JSONResponse({
                "success": True,
                "message": "反馈已提交，感谢您的参与！"
            })
    
    def _get_feedback_html(self) -> str:
        """生成反馈页面HTML"""
        
        return f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Augment Interactive Feedback</title>
            <style>
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #1a1a1a;
                    color: #e0e0e0;
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 10px;
                    color: white;
                }}
                .section {{
                    background-color: #2d2d2d;
                    padding: 20px;
                    margin: 20px 0;
                    border-radius: 8px;
                    border: 1px solid #404040;
                }}
                .command-section {{
                    background-color: #1e1e1e;
                    border: 1px solid #333;
                }}
                input, textarea, button {{
                    width: 100%;
                    padding: 10px;
                    margin: 10px 0;
                    border: 1px solid #555;
                    border-radius: 5px;
                    background-color: #333;
                    color: #e0e0e0;
                    font-family: inherit;
                }}
                button {{
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    cursor: pointer;
                    font-weight: bold;
                }}
                button:hover {{
                    background-color: #45a049;
                }}
                .execute-btn {{
                    background-color: #2196F3;
                }}
                .execute-btn:hover {{
                    background-color: #1976D2;
                }}
                #output {{
                    background-color: #000;
                    color: #00ff00;
                    font-family: 'Courier New', monospace;
                    padding: 15px;
                    border-radius: 5px;
                    white-space: pre-wrap;
                    max-height: 300px;
                    overflow-y: auto;
                    border: 1px solid #333;
                }}
                .info {{
                    background-color: #2d4a2d;
                    border-left: 4px solid #4CAF50;
                    padding: 10px;
                    margin: 10px 0;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🤖 Augment Interactive Feedback</h1>
                <p>AI助手正在等待您的反馈</p>
            </div>
            
            <div class="section">
                <h2>📋 AI工作摘要</h2>
                <div class="info">
                    {self.summary}
                </div>
                <p><strong>项目目录:</strong> {self.project_directory}</p>
            </div>
            
            <div class="section command-section">
                <h2>🔧 命令执行（可选）</h2>
                <p>您可以执行命令来验证AI的工作结果：</p>
                <input type="text" id="command" placeholder="输入要执行的命令，例如: npm test, python main.py, ls -la">
                <button class="execute-btn" onclick="executeCommand()">执行命令</button>
                <div id="output" style="display: none;"></div>
            </div>
            
            <div class="section">
                <h2>💬 您的反馈</h2>
                <textarea id="feedback" rows="6" placeholder="请提供您的反馈：
• 如果AI的工作满足要求，可以简单说"很好"或"完成"
• 如果需要修改，请详细说明需要改进的地方
• 如果有新的需求，请明确描述"></textarea>
                <button onclick="submitFeedback()">提交反馈</button>
            </div>
            
            <script>
                let commandLogs = '';
                
                async function executeCommand() {{
                    const command = document.getElementById('command').value;
                    if (!command) return;
                    
                    const output = document.getElementById('output');
                    output.style.display = 'block';
                    output.textContent = '执行中...';
                    
                    try {{
                        const response = await fetch('/execute', {{
                            method: 'POST',
                            headers: {{'Content-Type': 'application/x-www-form-urlencoded'}},
                            body: `command=${{encodeURIComponent(command)}}`
                        }});
                        
                        const result = await response.json();
                        output.textContent = result.output;
                        commandLogs += result.output + '\\n\\n';
                        
                    }} catch (error) {{
                        output.textContent = `执行失败: ${{error.message}}`;
                    }}
                }}
                
                async function submitFeedback() {{
                    const feedback = document.getElementById('feedback').value;
                    const command = document.getElementById('command').value;
                    
                    if (!feedback.trim()) {{
                        alert('请提供反馈内容');
                        return;
                    }}
                    
                    try {{
                        const response = await fetch('/submit', {{
                            method: 'POST',
                            headers: {{'Content-Type': 'application/x-www-form-urlencoded'}},
                            body: `feedback=${{encodeURIComponent(feedback)}}&logs=${{encodeURIComponent(commandLogs)}}&command=${{encodeURIComponent(command)}}`
                        }});
                        
                        const result = await response.json();
                        if (result.success) {{
                            document.body.innerHTML = `
                                <div class="header">
                                    <h1>✅ 反馈已提交</h1>
                                    <p>感谢您的反馈！AI助手将根据您的建议继续工作。</p>
                                    <p>您可以关闭此页面。</p>
                                </div>
                            `;
                        }}
                        
                    }} catch (error) {{
                        alert(`提交失败: ${{error.message}}`);
                    }}
                }}
                
                // 快捷键支持
                document.addEventListener('keydown', function(e) {{
                    if (e.ctrlKey && e.key === 'Enter') {{
                        submitFeedback();
                    }}
                }});
            </script>
        </body>
        </html>
        """
    
    def _run_server(self, port: int):
        """运行Web服务器"""
        uvicorn.run(self.app, host="127.0.0.1", port=port, log_level="error")
    
    def _find_free_port(self) -> int:
        """查找可用端口"""
        import socket
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            return s.getsockname()[1]
    
    def _fallback_feedback(self, project_directory: str, summary: str) -> Dict[str, Any]:
        """命令行fallback模式"""
        print(f"\\n{'='*60}")
        print(f"📋 AI工作摘要: {summary}")
        print(f"📁 项目目录: {project_directory}")
        print(f"{'='*60}")
        print("💬 请提供您的反馈: ", end="")
        
        feedback = input().strip()
        
        return {
            "success": True,
            "feedback": feedback,
            "logs": "",
            "command": ""
        }


# Augment工具接口
def web_interactive_feedback(
    project_directory: str = ".",
    summary: str = "我已完成了您请求的任务，请查看并提供反馈。",
    timeout: int = 600
) -> Dict[str, Any]:
    """
    基于Web的Augment交互式反馈工具
    
    Args:
        project_directory: 项目目录路径
        summary: AI工作摘要
        timeout: 超时时间（秒）
        
    Returns:
        用户反馈结果字典
    """
    
    tool = AugmentWebFeedback()
    return tool.request_feedback(project_directory, summary, timeout)


if __name__ == "__main__":
    # 测试
    result = web_interactive_feedback(
        summary="我已经创建了基于Web的交互反馈工具，请测试功能"
    )
    print(f"\\n结果: {result}")
