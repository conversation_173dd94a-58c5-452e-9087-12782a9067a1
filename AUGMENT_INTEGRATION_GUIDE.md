# Augment Interactive Feedback MCP 集成指南

## 🎯 概述

本指南将帮您在Augment中集成Interactive Feedback MCP，实现AI主动请求用户反馈的功能。

## ✅ 前置条件检查

- [x] Interactive Feedback MCP已安装在 `f:\study\interactive-feedback-mcp`
- [x] Python 3.11+ 环境可用
- [x] uv包管理器已安装
- [x] 所有依赖已通过 `uv sync` 安装完成

## 🔧 Augment配置步骤

### 步骤1: 配置MCP服务器

将以下配置添加到Augment的MCP设置中：

```json
{
  "mcpServers": {
    "interactive-feedback-mcp": {
      "command": "uv",
      "args": [
        "--directory",
        "f:\\study\\interactive-feedback-mcp",
        "run",
        "server.py"
      ],
      "timeout": 600,
      "autoApprove": [
        "interactive_feedback"
      ],
      "description": "Interactive User Feedback MCP - 允许AI主动请求用户反馈的工具",
      "env": {
        "PYTHONUNBUFFERED": "1"
      }
    }
  }
}
```

### 步骤2: 在Augment中添加提示词

为了让Augment Agent正确使用这个工具，建议在系统提示中添加以下内容：

```
## 交互式反馈使用规则

1. **任务完成确认**: 当你完成一个重要任务时，使用 `interactive_feedback` 工具请求用户确认
2. **关键决策点**: 在做出可能影响项目的重要决策前，先征求用户意见
3. **错误修复**: 修复问题后，让用户验证修复效果
4. **功能实现**: 实现新功能后，请用户测试并提供反馈

使用格式：
- project_directory: 当前工作的项目目录路径
- summary: 简洁描述你完成的工作和需要用户确认的内容
- timeout: 等待用户反馈的时间（默认600秒）

示例：
```
我已经实现了用户登录功能，包括表单验证和错误处理。请测试登录流程并提供反馈。
```
```

### 步骤3: 验证配置

1. **重启Augment**: 重新启动Augment以加载新的MCP配置
2. **检查连接**: 确认Augment能够连接到Interactive Feedback MCP服务器
3. **测试工具**: 尝试使用 `interactive_feedback` 工具

## 🎮 使用示例

### 基本使用

```
请使用interactive_feedback工具，告诉用户你已经完成了文件创建任务，请求他们的反馈。
```

### 高级使用

```
我刚刚修改了数据库配置文件，请使用interactive_feedback工具：
- 项目目录设置为当前工作目录
- 摘要说明我修改了什么以及为什么修改
- 请用户验证配置是否正确
```

## 🔍 故障排除

### 常见问题

1. **MCP服务器无法启动**
   - 检查路径 `f:\study\interactive-feedback-mcp` 是否正确
   - 确认uv和Python环境可用
   - 查看Augment的MCP日志

2. **工具调用失败**
   - 确认autoApprove配置正确
   - 检查timeout设置是否合理
   - 验证项目目录路径存在

3. **UI界面不显示**
   - 确认PySide6依赖已安装
   - 检查系统是否支持GUI应用
   - 查看是否有防火墙阻止

### 调试命令

```bash
# 测试MCP服务器
cd f:\study\interactive-feedback-mcp
uv run server.py

# 测试开发模式
uv run fastmcp dev server.py

# 检查依赖
uv run python -c "import fastmcp, psutil, PySide6; print('All dependencies OK')"
```

## 🚀 高级配置

### 自定义超时时间

```json
{
  "interactive-feedback-mcp": {
    "timeout": 1200,
    "args": [
      "--directory",
      "f:\\study\\interactive-feedback-mcp",
      "run",
      "server.py"
    ]
  }
}
```

### 环境变量配置

```json
{
  "interactive-feedback-mcp": {
    "env": {
      "PYTHONUNBUFFERED": "1",
      "MCP_DEBUG": "false",
      "UI_THEME": "dark"
    }
  }
}
```

## 📊 使用效果

集成后，您将获得：

- ✅ **主动确认**: AI会在关键节点主动请求确认
- ✅ **实时反馈**: 通过GUI界面提供直观的反馈体验
- ✅ **命令验证**: 可以直接在界面中执行命令验证结果
- ✅ **成本优化**: 减少不必要的AI工具调用
- ✅ **更好控制**: 用户对AI行为有更强的控制感

## 🔄 工作流程示例

1. **AI完成任务** → 调用 `interactive_feedback`
2. **弹出反馈界面** → 显示AI工作摘要
3. **用户验证** → 执行命令检查结果
4. **提供反馈** → 文字描述满意度或改进建议
5. **AI继续工作** → 根据反馈调整后续行为

## 📞 支持

如果遇到问题：

1. 查看本指南的故障排除部分
2. 检查Interactive Feedback MCP的官方文档
3. 确认Augment的MCP配置语法正确

---

**配置完成后，请重启Augment并测试interactive_feedback工具的功能！**
