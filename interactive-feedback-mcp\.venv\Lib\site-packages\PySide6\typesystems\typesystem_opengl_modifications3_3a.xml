<!--
// Copyright (C) 2019 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only

Present from 3.3..4.5 except 4.4 Core/4.5 Core
-->
<modify-function signature="^glColorP[34]uiv\(.*$">
    <modify-argument index="2"><array/></modify-argument>
</modify-function>
<modify-function signature="^glMultiTexCoordP\duiv\(.*$">
    <modify-argument index="3"><array/></modify-argument>
</modify-function>
<modify-function signature="^gl(Normal|SecondaryColor)P3uiv\(.*$">
    <modify-argument index="2"><array/></modify-argument>
</modify-function>
<modify-function signature="^glTexCoordP\duiv\(.*$">
    <modify-argument index="2"><array/></modify-argument>
</modify-function>
<modify-function signature="^glVertexP\duiv\(.*$">
    <modify-argument index="2"><array/></modify-argument>
</modify-function>
