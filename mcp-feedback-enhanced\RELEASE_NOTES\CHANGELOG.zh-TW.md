# 更新日誌 (繁體中文)

本文件記錄了 **MCP Feedback Enhanced** 的所有版本更新內容。

## [v2.4.3] - 2025-06-14 - 會話管理重構與音效通知

### 🌟 版本亮點
將會話管理從左側邊欄遷移到獨立頁籤，解決瀏覽器相容性問題。新增音效通知系統，支援自訂音效。

### ✨ 新功能
- 🔊 **音效通知系統**: 會話更新時播放音效提醒，支援內建音效和自訂音效上傳
- 📚 **會話歷史管理**: 本地保存會話記錄，支援匯出和清理功能
- 💾 **輸入框高度記憶**: 自動保存和恢復文字輸入框的高度設定
- 📋 **一鍵複製**: 專案路徑和會話ID支援點擊複製

### 🚀 改進功能
- 📋 **會話管理重構**: 從左側邊欄遷移到「會話管理」頁籤，解決小視窗下按鈕無法點擊的問題
- 🎨 **介面佈局優化**: AI摘要區域自動擴展，提交按鈕位置調整，移除多餘描述文字
- 🌐 **多語言增強**: 新增tooltip和按鈕的多語言支援

### 🐛 問題修復
- 修復當前會話詳細資訊按鈕無反應問題
- 修復會話詳情彈窗關閉延遲問題
- 修復音效通知語系初始化問題
- 修正自動提交處理邏輯

---

## [v2.4.2] - Web-Only 架構重構與智能功能增強

### 🌟 版本亮點
本版本進行了重大架構重構，**完全移除 PyQt6 GUI 依賴**，轉為純 Web UI 架構，大幅簡化部署和維護。同時新增多項智能功能，包括提示詞管理、自動提交、會話管理等，全面提升用戶體驗和工作效率。

### 🔄 重大架構變更
- 🏗️ **完全移除 PyQt6 GUI**: 徹底移除桌面應用程式依賴，簡化安裝和部署流程
- 🌐 **純 Web UI 架構**: 統一使用 Web 介面，支援所有平台和環境
- 📦 **依賴大幅簡化**: 移除 PyQt6、相關 GUI 庫等重型依賴，安裝包體積顯著減小
- 🚀 **部署更簡單**: 無需考慮 GUI 環境配置，適用於所有開發環境

### ✨ 全新功能
- 📝 **智能提示詞管理系統**:
  - 常用提示詞的 CRUD 操作（新增、編輯、刪除、使用）
  - 使用頻率統計和智能排序
  - 快速選擇和一鍵應用功能
  - 支援自動提交標記和優先顯示
- ⏰ **自動定時提交功能**:
  - 可設定 1-86400 秒的倒數計時器
  - 視覺化倒數顯示和狀態指示
  - 與提示詞管理系統深度整合
  - 支援暫停、恢復、取消操作
- 📊 **會話管理與追蹤**:
  - 當前會話狀態即時顯示
  - 會話歷史記錄和統計分析
  - 今日會話數量和平均時長統計
  - 會話詳情查看和管理功能
- 🔗 **連線監控系統**:
  - WebSocket 連線狀態即時監控
  - 延遲測量和連線品質指示
  - 自動重連機制和錯誤處理
  - 詳細的連線統計資訊
- ⌨️ **快捷鍵增強**: 新增 Ctrl+I 快速聚焦輸入框功能 (感謝 @penn201500)

### 🚀 功能改進
- 🎨 **UI/UX 全面優化**:
  - 新增左側會話管理面板，支援收合/展開
  - 頂部連線狀態列，即時顯示系統狀態
  - 響應式設計，適配不同螢幕尺寸
  - 統一的設計語言和視覺風格
- 🌐 **多語言系統增強**:
  - 優化語言切換機制，支援即時切換
  - 新增大量翻譯文本，提升本地化覆蓋率
  - 改進語言選擇器 UI，使用下拉選單設計
  - 修復語言切換時的顯示問題
- 🖼️ **圖片設定整合**:
  - 將圖片設定從工作區移至設定頁籤
  - 統一的設定管理介面
  - 改進設定項目的組織和佈局
- 📱 **介面佈局優化**:
  - 調整版面配置，符合多語言顯示需求
  - 優化按鈕樣式和間距
  - 改進表單元素的視覺設計
  - 增強可訪問性和易用性

### 🐛 問題修復
- 🔧 **會話管理修復**:
  - 修復會話統計資訊無法正確更新的問題
  - 修復會話數量計算錯誤
  - 改進會話狀態追蹤機制
- 🎯 **提示詞功能修復**:
  - 修復常用提示詞管理無法正確設定自動提交的問題
  - 改進提示詞選擇和應用邏輯
- 🌐 **語系切換修復**:
  - 修復語言切換時部分文字未更新的問題
  - 改進多語言文本的載入機制
- 🏗️ **架構穩定性修復**:
  - 修復會話管理初始化問題
  - 改進錯誤處理和資源清理
  - 優化模組載入順序和依賴關係

### 🛠️ 技術改進
- 📦 **模組化架構**:
  - JavaScript 代碼完全模組化重構
  - 採用 ES6+ 語法和現代化開發模式
  - 清晰的模組分離和職責劃分
- 📊 **效能提升**:
  - 優化 WebSocket 通信效率
  - 改進前端資源載入速度
  - 減少記憶體使用和 CPU 負載

### 📚 文檔更新
- 📖 **架構文檔更新**: 更新系統架構說明，反映 Web-Only 設計
- 🔧 **安裝指南簡化**: 移除 GUI 相關安裝步驟和依賴說明
- 🖼️ **截圖更新**: 更新所有介面截圖，展示新的 Web UI 設計
- 📋 **API 文檔增強**: 新增提示詞管理、自動提交等新功能的 API 說明

---

## [v2.3.0] - 系統穩定性與資源管理增強

### 🌟 亮點
本版本專注於提升系統穩定性和使用體驗，特別解決了 Cursor SSH Remote 環境下無法啟動瀏覽器的問題。

### ✨ 新功能
- 🌐 **SSH Remote 環境支援**: 解決 Cursor SSH Remote 無法啟動瀏覽器的問題，提供清晰的使用指引
- 🛡️ **錯誤提示改善**: 當發生錯誤時，提供更友善的錯誤訊息和解決建議
- 🧹 **自動清理功能**: 自動清理臨時文件和過期會話，保持系統整潔
- 📊 **記憶體監控**: 監控記憶體使用情況，防止系統資源不足

### 🚀 改進功能
- 💾 **資源管理優化**: 更好地管理系統資源，提升運行效率
- 🔧 **錯誤處理增強**: 遇到問題時提供更清楚的說明和解決方案
- 🌐 **連線穩定性**: 改善 Web UI 的連線穩定性
- 🖼️ **圖片上傳優化**: 改善圖片上傳功能的穩定性
- 🎯 **自動聚焦輸入框**: 回饋視窗開啟時自動聚焦到輸入框，提升用戶體驗 (感謝 @penn201500)

### 🐛 問題修復
- 🌐 **連線問題**: 修復 WebSocket 連線的相關問題
- 🔄 **會話管理**: 修復會話狀態追蹤的問題
- 🖼️ **圖片處理**: 修復圖片上傳時的事件處理問題

---

## [v2.2.5] - WSL 環境支援與跨平台增強

### ✨ 新功能
- 🐧 **WSL 環境檢測**: 自動識別 WSL 環境，提供專門的支援邏輯
- 🌐 **智能瀏覽器啟動**: WSL 環境下自動調用 Windows 瀏覽器，支援多種啟動方式
- 🔧 **跨平台測試增強**: 測試功能整合 WSL 檢測，提升測試覆蓋率

### 🚀 改進功能
- 🎯 **環境檢測優化**: 改進遠端環境檢測邏輯，WSL 不再被誤判為遠端環境
- 📊 **系統資訊增強**: 系統資訊工具新增 WSL 環境狀態顯示
- 🧪 **測試體驗提升**: 測試模式下自動嘗試啟動瀏覽器，提供更好的測試體驗

---

## [v2.2.4] - GUI 體驗優化與問題修復

### 🐛 問題修復
- 🖼️ **圖片重複貼上修復**: 解決 GUI 介面中使用 Ctrl+V 複製貼上圖片時出現重複貼上的問題
- 🌐 **語系切換修復**: 修復圖片設定區域在語言切換時文字沒有正確翻譯的問題
- 📝 **字體可讀性改善**: 調整圖片設定區域的字體大小，提升文字可讀性

---

## [v2.2.3] - 超時控制與圖片設定增強

### ✨ 新功能
- ⏰ **用戶超時控制**: 新增可自訂的超時設定功能，支援 30 秒至 2 小時的彈性設定
- ⏱️ **倒數計時器**: 介面頂部顯示即時倒數計時器，提供視覺化的時間提醒
- 🖼️ **圖片大小限制**: 新增圖片上傳大小限制設定（無限制/1MB/3MB/5MB）
- 🔧 **Base64 相容模式**: 新增 Base64 詳細模式，提升部分 AI 模型的圖片識別相容性
- 🧹 **UV Cache 管理工具**: 新增 `cleanup_cache.py` 腳本，協助管理和清理 UV cache 空間

### 🚀 改進功能
- 📚 **文檔結構優化**: 重新整理文檔目錄結構，將圖片移至 `docs/{語言}/images/` 路徑
- 📖 **Cache 管理指南**: 新增詳細的 UV Cache 管理指南，包含自動化清理方案
- 🎯 **智能相容性提示**: 當圖片上傳失敗時自動顯示 Base64 相容模式建議

### 🐛 問題修復
- 🛡️ **超時處理優化**: 改進用戶自訂超時與 MCP 系統超時的協調機制
- 🖥️ **介面自動關閉**: 修復超時後介面自動關閉和資源清理邏輯
- 📱 **響應式佈局**: 優化超時控制元件在小螢幕設備上的顯示效果

---

## [v2.2.2] - 超時自動清理修復

### 🐛 問題修復
- 🔄 **超時自動清理**: 修復 GUI/Web UI 在 MCP session timeout (預設 600 秒) 後沒有自動關閉的問題
- 🛡️ **資源管理優化**: 改進超時處理機制，確保在超時時正確清理和關閉所有 UI 資源
- ⚡ **超時檢測增強**: 加強超時檢測邏輯，確保在各種情況下都能正確處理超時事件

---

## [v2.2.1] - 視窗優化與統一設定接口

### 🚀 改進功能
- 🖥️ **視窗大小限制解除**: 解除 GUI 主視窗最小大小限制，從 1000×800 降至 400×300
- 💾 **視窗狀態實時保存**: 實現視窗大小與位置的即時保存機制，支援防抖延遲
- ⚙️ **統一設定接口優化**: 改進 GUI 設定版面的配置保存邏輯，避免設定衝突

### 🐛 問題修復
- 🔧 **視窗大小限制**: 解決 GUI 視窗無法調整至小尺寸的問題
- 🛡️ **設定衝突**: 修復設定保存時可能出現的配置衝突問題

---

## [v2.2.0] - 佈局與設定界面優化

### ✨ 新功能
- 🎨 **水平佈局模式**: GUI 與 Web UI 的合併模式新增摘要與回饋的左右佈局選項

### 🚀 改進功能
- 🎨 **設定界面改進**: 優化了 GUI 與 Web UI 的設定頁面，提升佈局清晰度
- ⌨️ **快捷鍵完善**: 提交回饋快捷鍵現已完整支援數字鍵盤的 Enter 鍵

### 🐛 問題修復
- 🔧 **圖片重複貼上**: 解決了在 Web UI 文字輸入區使用 Ctrl+V 貼上圖片時的重複問題

---

## [v2.1.1] - 視窗定位優化

### ✨ 新功能
- 🖥️ **智能視窗定位**: 新增「總是在主螢幕中心顯示視窗」設定選項
- 🌐 **多螢幕支援**: 完美解決 T 字型螢幕排列等複雜多螢幕環境的視窗定位問題
- 💾 **位置記憶**: 自動保存和恢復視窗位置，智能檢測視窗可見性

---

## [v2.1.0] - 全面重構版

### 🎨 重大重構
- 🏗️ **全面重構**: GUI 和 Web UI 採用模組化架構
- 📁 **集中管理**: 重新組織資料夾結構，提升維護性
- 🖥️ **界面優化**: 現代化設計和改進的用戶體驗

### ✨ 新功能
- 🍎 **macOS 界面優化**: 針對 macOS 用戶體驗進行專項改進
- ⚙️ **功能增強**: 新增設定選項和自動關閉頁面功能
- ℹ️ **關於頁面**: 新增關於頁面，包含版本資訊、專案連結和致謝內容

---

## [v2.0.14] - 快捷鍵與圖片功能增強

### 🚀 改進功能
- ⌨️ **增強快捷鍵**: Ctrl+Enter 支援數字鍵盤
- 🖼️ **智能圖片貼上**: Ctrl+V 直接貼上剪貼簿圖片

---

## [v2.0.9] - 多語言架構重構

### 🔄 重構
- 🌏 **多語言架構重構**: 支援動態載入
- 📁 **語言檔案模組化**: 模組化組織語言檔案

---

## [v2.0.3] - 編碼問題修復

### 🐛 重要修復
- 🛡️ **完全修復中文字符編碼問題**: 解決所有中文顯示相關問題
- 🔧 **解決 JSON 解析錯誤**: 修復資料解析錯誤

---

## [v2.0.0] - Web UI 支援

### 🌟 重大功能
- ✅ **新增 Web UI 支援**: 支援遠端環境使用
- ✅ **自動環境檢測**: 自動選擇合適的界面
- ✅ **WebSocket 即時通訊**: 實現即時雙向通訊

---

## 圖例說明

| 圖示 | 意義 |
|------|------|
| 🌟 | 版本亮點 |
| ✨ | 新功能 |
| 🚀 | 改進功能 |
| 🐛 | 問題修復 |
| 🔄 | 重構變更 |
| 🎨 | 界面優化 |
| ⚙️ | 設定相關 |
| 🖥️ | 視窗相關 |
| 🌐 | 多語言/網路相關 |
| 📁 | 檔案結構 |
| ⌨️ | 快捷鍵 |
| 🖼️ | 圖片功能 |
| 📝 | 提示詞管理 |
| ⏰ | 自動提交 |
| 📊 | 會話管理 |
| 🔗 | 連線監控 |
| 🏗️ | 架構變更 |
| 🛠️ | 技術改進 |
| 📚 | 文檔更新 |

---

**完整專案資訊：** [GitHub - mcp-feedback-enhanced](https://github.com/Minidoracat/mcp-feedback-enhanced)
