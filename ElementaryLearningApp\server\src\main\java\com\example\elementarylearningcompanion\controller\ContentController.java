package com.example.elementarylearningcompanion.controller;

import com.example.elementarylearningcompanion.model.Character;
import com.example.elementarylearningcompanion.model.Lesson;
import com.example.elementarylearningcompanion.model.TextbookVersion;
import com.example.elementarylearningcompanion.service.ContentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/content")
public class ContentController {

    @Autowired
    private ContentService contentService;

    @GetMapping("/textbooks")
    public ResponseEntity<List<TextbookVersion>> getTextbookVersions(
            @RequestParam(required = false) String subject) {
        List<TextbookVersion> versions;
        if (subject != null && !subject.isEmpty()) {
            versions = contentService.getTextbookVersionsBySubject(subject);
        } else {
            versions = contentService.getAllTextbookVersions();
        }
        return ResponseEntity.ok(versions);
    }

    @GetMapping("/lessons")
    public ResponseEntity<List<Lesson>> getLessons(
            @RequestParam Integer textbookVersionId,
            @RequestParam Integer grade) {
        List<Lesson> lessons = contentService.getLessons(textbookVersionId, grade);
        return ResponseEntity.ok(lessons);
    }

    @GetMapping("/lessons/{id}")
    public ResponseEntity<Lesson> getLessonDetails(@PathVariable Long id) {
        return contentService.getLessonDetails(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/lessons/{lessonId}/characters")
    public ResponseEntity<List<Character>> getCharactersByLesson(@PathVariable Long lessonId) {
        List<Character> characters = contentService.getCharactersByLesson(lessonId);
        return ResponseEntity.ok(characters);
    }
}
