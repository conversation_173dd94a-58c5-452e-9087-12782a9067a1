# 项目名称：小学伴读 (Elementary Learning Companion)

## 1. 项目愿景

打造一款集趣味性、个性化和智能化于一体的小学全科（语文、数学、英语）学习辅助APP。旨在激发小学生的学习兴趣，培养自主学习习惯，同时为家长提供一个清晰、便捷的子女学习情况追踪与指导平台。

## 2. 用户角色与画像

### 2.1. 学生用户 (6-12岁)
*   **需求**:
    *   以有趣、不枯燥的方式学习新知识。
    *   复习课堂内容，巩固知识点。
    *   通过练习和测试检验自己的学习成果。
    *   获得及时的鼓励和正向反馈。
*   **痛点**:
    *   传统学习方式单调，容易分心。
    *   遇到难题时无人及时解答。
    *   对自己的学习水平没有清晰的认知。

### 2.2. 家长用户
*   **需求**:
    *   了解孩子的学习进度和薄弱环节。
    *   辅导孩子功课，但可能缺乏时间和专业知识。
    *   控制孩子的屏幕使用时间。
    *   获取科学的教育建议和资源。
*   **痛点**:
    *   工作繁忙，无法时刻监督孩子学习。
    *   不了解孩子的真实学习情况，只能看考试成绩。
    *   与孩子在学习问题上容易产生冲突。

## 3. 核心功能模块

### 3.1. 学生端

#### 3.1.1. 学习中心 (同步教材)
*   **多版本教材支持**: 学生首次使用时可选择所在年级和所用教材版本（如：人教版、苏教版、北师大版等），APP内容将与所选教材完全同步。
*   **语文**:
    *   课文跟读：标准发音朗读，支持点读、跟读、录音对比。
    *   生字学习：笔顺动画、拼音、组词、造句。
    *   古诗词/文言文：动画讲解、跟读、背诵模式。
*   **数学**:
    *   知识点动画：将抽象的数学概念（如加减乘除、几何图形）用动画形式展现。
    *   互动练习：拖拽、连线等游戏化练习，即时反馈。
    *   口算训练：限时挑战，提升计算速度和准确率。
*   **英语**:
    *   单词学习：图片、发音、例句、拼写练习。
    *   课文/对话跟读：模拟真实语境，支持角色扮演。
    *   自然拼读：系统性的拼读规则学习和练习。

#### 3.1.2. 复习与测试
*   **智能复习**: 根据艾宾浩斯遗忘曲线和学生练习记录，智能推送需要复习的知识点。
*   **错题本**: 自动收录所有做错的题目，支持分类查看和重做。
*   **专项练习**: 针对特定知识点（如：语文的阅读理解、数学的应用题）进行强化训练。
*   **模拟考试**: 定期生成单元、期中、期末模拟试卷，模拟真实考试环境，并生成详细分析报告。

#### 3.1.3. 趣味与激励
*   **知识岛屿冒险**: 将学习路径设计为一场海岛冒险，每个知识点是一个关卡，学生通过答题闯关，解锁宝藏和新岛屿。
*   **每日挑战**: 每天推出一组跨学科的趣味挑战题，完成可获得额外奖励。
*   **成就系统**: 完成学习任务、达到正确率等可获得积分和成就徽章。
*   **宠物/装扮系统**: 使用积分可以兑换虚拟宠物或个性化装扮，增加趣味性。

#### 3.1.4. 健康与关怀
*   **护眼模式**: 提供多种柔和的界面主题（如豆沙绿背景），支持字体大小调节，减少视觉疲劳。
*   **定时休息提醒**: 每学习30分钟，系统将强制弹出休息提醒，并伴有眼保健操或远眺引导动画，休息5分钟后方可继续学习。

#### 3.1.5. 自主学习引导
*   **智能学习计划**: APP可以根据学生的学习进度、薄弱环节和设定的目标（如“期中考试复习”），为学生智能生成每日/每周的学习计划，引导他们有条理地进行学习。
*   **目标设定与追踪**: 学生可以自己或在家长协助下设定学习小目标（如“本周掌握20个单词”），APP会通过数据可视化追踪目标完成进度，给予激励。
*   **探索式学习**: 提供知识图谱功能，学生在学习一个知识点时，可以自由探索其关联内容（如学习生字时，可以探索其部首、相关成语、同音字等），培养发散性思维和探索欲。

### 3.2. 家长端

#### 3.2.1. 学习报告
*   **每日/每周报告**: 直观展示孩子的学习时长、学习内容、正确率、薄弱知识点等。
*   **能力雷达图**: 从计算能力、阅读能力、词汇量等多个维度分析孩子的能力。
*   **考试报告分析**: 对模拟考试结果进行深度分析，提供学习建议。

#### 3.2.2. 家长监控与互动
*   **学习计划**: 家长可以为孩子设置每日学习任务和时长。
*   **学习时长管理**: 家长可精细化设置孩子每日/每周的总学习时长和允许使用时段。超时后APP将自动进入锁定状态，保护孩子视力，防止沉迷。
*   **亲子任务**: 发布一些需要家长和孩子共同完成的学习任务，增进亲子关系。

## 4. 技术栈选型 (建议)

*   **客户端**: Kotlin / Jetpack Compose / Flutter (跨平台方案)
*   **后端**: Spring Boot / Python Django / Node.js
*   **数据库**: MySQL / PostgreSQL
*   **数据存储/缓存**: Redis
*   **AI/算法**:
    *   知识图谱：构建学科知识点关联网络。
    *   推荐算法：实现个性化学习路径和题目推荐。
    *   语音评测：集成第三方SDK实现中英文发音评测。
*   **部署**: Docker / Kubernetes / 云服务器 (如阿里云, 腾讯云)

## 5. 盈利模式 (思考)

*   **基础功能免费**: 提供大部分核心学习功能免费使用，吸引用户。
*   **会员订阅 (VIP)**:
    *   解锁高级功能：如AI老师问答、深度学习报告、无限次模拟考试等。
    *   解锁精品学习资源：如名师讲解视频、奥数拓展课程等。
*   **内容付费**: 购买特定的专题课程包。

## 6. 开发流程与迭代方法

为确保项目成果紧密贴合您的设想并能及时调整，我们将采用敏捷、迭代的开发模式。

*   **分阶段交付**: 我们将严格按照《TODO任务清单》的阶段划分进行开发。每完成一个或数个关键模块（例如，完成“核心学习功能”），就会打包生成一个可安装、可运行的测试版本（APK文件）。
*   **反馈驱动调整**: 您在体验了每个测试版本后，可以及时提出修改意见和新的想法。我们会将您的反馈整合到后续的开发计划中，进行微调和优化，避免在项目后期进行大规模返工。
*   **透明化进程**: 整个开发过程将保持高度透明，确保您能随时了解项目进展。
