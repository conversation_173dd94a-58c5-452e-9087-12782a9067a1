package com.example.elementarylearningcompanion.repository;

import com.example.elementarylearningcompanion.model.Lesson;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LessonRepository extends JpaRepository<Lesson, Long> {

    List<Lesson> findByTextbookVersionIdAndGrade(Integer textbookVersionId, Integer grade);

}
