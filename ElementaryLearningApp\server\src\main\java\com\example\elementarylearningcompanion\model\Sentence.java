package com.example.elementarylearningcompanion.model;

import jakarta.persistence.*;

@Entity
@Table(name = "sentences")
public class Sentence {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "lesson_id", nullable = false)
    private Lesson lesson;

    @Column(nullable = false)
    private Integer sentenceOrder;

    @Column(nullable = false, columnDefinition = "TEXT")
    private String textContent;

    private String audioUrl;
    private Float startTimestamp;
    private Float endTimestamp;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Lesson getLesson() {
        return lesson;
    }

    public void setLesson(Lesson lesson) {
        this.lesson = lesson;
    }

    public Integer getSentenceOrder() {
        return sentenceOrder;
    }

    public void setSentenceOrder(Integer sentenceOrder) {
        this.sentenceOrder = sentenceOrder;
    }

    public String getTextContent() {
        return textContent;
    }

    public void setTextContent(String textContent) {
        this.textContent = textContent;
    }

    public String getAudioUrl() {
        return audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public Float getStartTimestamp() {
        return startTimestamp;
    }

    public void setStartTimestamp(Float startTimestamp) {
        this.startTimestamp = startTimestamp;
    }

    public Float getEndTimestamp() {
        return endTimestamp;
    }

    public void setEndTimestamp(Float endTimestamp) {
        this.endTimestamp = endTimestamp;
    }
}
