{"logs": [{"outputFile": "com.example.elementarylearningcompanion.app-mergeDebugResources-58:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0a92ec79585cffeb394139b023740469\\transformed\\material-1.12.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,332,400,475,557,638,727,829,906,965,1029,1114,1176,1234,1319,1382,1444,1502,1568,1630,1685,1781,1838,1897,1953,2020,2125,2205,2286,2378,2463,2544,2673,2746,2817,2931,3013,3089,3140,3191,3257,3323,3396,3467,3542,3610,3683,3754,3821,3919,4004,4071,4158,4246,4320,4388,4473,4524,4602,4666,4746,4828,4890,4954,5017,5083,5178,5273,5358,5449,5504,5559,5635,5714,5789", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,70,67,74,81,80,88,101,76,58,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75,78,74,70", "endOffsets": "256,327,395,470,552,633,722,824,901,960,1024,1109,1171,1229,1314,1377,1439,1497,1563,1625,1680,1776,1833,1892,1948,2015,2120,2200,2281,2373,2458,2539,2668,2741,2812,2926,3008,3084,3135,3186,3252,3318,3391,3462,3537,3605,3678,3749,3816,3914,3999,4066,4153,4241,4315,4383,4468,4519,4597,4661,4741,4823,4885,4949,5012,5078,5173,5268,5353,5444,5499,5554,5630,5709,5784,5855"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,53,54,55,56,59,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,182,183,185", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2929,3000,3068,3143,3225,3993,4082,4184,4691,4750,4814,4899,5122,10964,11049,11112,11174,11232,11298,11360,11415,11511,11568,11627,11683,11750,11855,11935,12016,12108,12193,12274,12403,12476,12547,12661,12743,12819,12870,12921,12987,13053,13126,13197,13272,13340,13413,13484,13551,13649,13734,13801,13888,13976,14050,14118,14203,14254,14332,14396,14476,14558,14620,14684,14747,14813,14908,15003,15088,15179,15234,15581,15896,15975,16120", "endLines": "5,33,34,35,36,37,45,46,47,53,54,55,56,59,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,182,183,185", "endColumns": "12,70,67,74,81,80,88,101,76,58,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75,78,74,70", "endOffsets": "306,2995,3063,3138,3220,3301,4077,4179,4256,4745,4809,4894,4956,5175,11044,11107,11169,11227,11293,11355,11410,11506,11563,11622,11678,11745,11850,11930,12011,12103,12188,12269,12398,12471,12542,12656,12738,12814,12865,12916,12982,13048,13121,13192,13267,13335,13408,13479,13546,13644,13729,13796,13883,13971,14045,14113,14198,14249,14327,14391,14471,14553,14615,14679,14742,14808,14903,14998,15083,15174,15229,15284,15652,15970,16045,16186"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\95602d9e58c05302d7b199e835d43608\\transformed\\appcompat-1.6.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,409,507,613,699,802,919,997,1073,1164,1257,1349,1443,1543,1636,1731,1824,1915,2006,2086,2186,2286,2382,2484,2584,2683,2833,15816", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "404,502,608,694,797,914,992,1068,1159,1252,1344,1438,1538,1631,1726,1819,1910,2001,2081,2181,2281,2377,2479,2579,2678,2828,2924,15891"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b39f3108a82aedf46ce308be2bee9e64\\transformed\\jetified-material3-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,276,384,491,585,675,782,910,1020,1149,1231,1329,1416,1509,1619,1738,1841,1964,2089,2213,2361,2477,2590,2704,2819,2907,3002,3112,3231,3326,3428,3530,3650,3776,3880,3976,4050,4143,4235,4319,4404,4506,4587,4670,4770,4867,4962,5057,5142,5244,5343,5442,5560,5641,5742", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "161,271,379,486,580,670,777,905,1015,1144,1226,1324,1411,1504,1614,1733,1836,1959,2084,2208,2356,2472,2585,2699,2814,2902,2997,3107,3226,3321,3423,3525,3645,3771,3875,3971,4045,4138,4230,4314,4399,4501,4582,4665,4765,4862,4957,5052,5137,5239,5338,5437,5555,5636,5737,5834"}, "to": {"startLines": "60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5180,5291,5401,5509,5616,5710,5800,5907,6035,6145,6274,6356,6454,6541,6634,6744,6863,6966,7089,7214,7338,7486,7602,7715,7829,7944,8032,8127,8237,8356,8451,8553,8655,8775,8901,9005,9101,9175,9268,9360,9444,9529,9631,9712,9795,9895,9992,10087,10182,10267,10369,10468,10567,10685,10766,10867", "endColumns": "110,109,107,106,93,89,106,127,109,128,81,97,86,92,109,118,102,122,124,123,147,115,112,113,114,87,94,109,118,94,101,101,119,125,103,95,73,92,91,83,84,101,80,82,99,96,94,94,84,101,98,98,117,80,100,96", "endOffsets": "5286,5396,5504,5611,5705,5795,5902,6030,6140,6269,6351,6449,6536,6629,6739,6858,6961,7084,7209,7333,7481,7597,7710,7824,7939,8027,8122,8232,8351,8446,8548,8650,8770,8896,9000,9096,9170,9263,9355,9439,9524,9626,9707,9790,9890,9987,10082,10177,10262,10364,10463,10562,10680,10761,10862,10959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\436103b527b98b869694e03887dd212f\\transformed\\jetified-foundation-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,85", "endOffsets": "137,223"}, "to": {"startLines": "190,191", "startColumns": "4,4", "startOffsets": "16553,16640", "endColumns": "86,85", "endOffsets": "16635,16721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a90387b92ad49178bbd95d8e99c39d57\\transformed\\core-1.13.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "38,39,40,41,42,43,44,186", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3306,3399,3499,3596,3695,3791,3893,16191", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "3394,3494,3591,3690,3786,3888,3988,16287"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a579e95c6560c5b53f12b3ba87ce1938\\transformed\\jetified-ui-release\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,188,265,357,453,535,613,696,778,856,922,988,1066,1147,1217,1297,1362", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "183,260,352,448,530,608,691,773,851,917,983,1061,1142,1212,1292,1357,1473"}, "to": {"startLines": "48,49,50,51,52,57,58,174,175,176,177,179,180,184,187,188,189", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4261,4344,4421,4513,4609,4961,5039,15289,15371,15449,15515,15657,15735,16050,16292,16372,16437", "endColumns": "82,76,91,95,81,77,82,81,77,65,65,77,80,69,79,64,115", "endOffsets": "4339,4416,4508,4604,4686,5034,5117,15366,15444,15510,15576,15730,15811,16115,16367,16432,16548"}}]}]}