<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2021 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtMultimediaWidgets" doc-package="PySide6.QtMultimedia"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
  <load-typesystem name="typesystem_core.xml" generate="no"/>
  <load-typesystem name="typesystem_gui.xml" generate="no"/>
  <load-typesystem name="typesystem_multimedia.xml" generate="no"/>
  <load-typesystem name="typesystem_widgets.xml" generate="no"/>

  <object-type name="QGraphicsVideoItem"/>
  <object-type name="QVideoWidget"/>
</typesystem>
