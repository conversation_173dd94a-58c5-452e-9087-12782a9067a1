# Interactive Feedback MCP 安装指南

## 安装状态
✅ **已完成安装！**

## 安装位置
- 项目目录: `f:\study\interactive-feedback-mcp`
- Python版本要求: >=3.11 ✅
- uv包管理器: 已安装 ✅
- 项目依赖: 已安装 ✅

## 已安装的依赖包
- fastmcp>=2.0.0
- psutil>=7.0.0  
- pyside6>=6.8.2.1
- 以及其他36个依赖包

## 运行方式

### 1. 开发模式（推荐用于测试）
```bash
cd f:\study\interactive-feedback-mcp
uv run fastmcp dev server.py
```
这将启动一个Web界面用于测试MCP工具。

### 2. 生产模式（用于Cursor集成）
```bash
cd f:\study\interactive-feedback-mcp
uv run server.py
```

## Cursor配置

### 方法1: 使用提供的配置文件
将 `cursor-mcp-config.json` 的内容添加到您的Cursor MCP配置中。

### 方法2: 手动配置
在Cursor的MCP设置中添加以下配置：

```json
{
  "mcpServers": {
    "interactive-feedback-mcp": {
      "command": "uv",
      "args": [
        "--directory",
        "f:\\study\\interactive-feedback-mcp",
        "run",
        "server.py"
      ],
      "timeout": 600,
      "autoApprove": [
        "interactive_feedback"
      ]
    }
  }
}
```

## 使用建议

### 在Cursor中添加提示词
为了获得最佳效果，请在Cursor的自定义提示中添加以下内容：

> Whenever you want to ask a question, always call the MCP `interactive_feedback`.  
> Whenever you're about to complete a user request, call the MCP `interactive_feedback` instead of simply ending the process. Keep calling MCP until the user's feedback is empty, then end the request.

## 功能特性
- 🔄 人机交互循环工作流
- 💬 实时用户反馈收集
- 🛠️ 命令执行和输出查看
- 💾 项目级配置保存
- 🎯 减少AI工具调用成本

## 测试
当前MCP服务器正在开发模式下运行，您可以通过浏览器访问测试界面。

## 故障排除
如果遇到问题：
1. 确保Python版本>=3.11
2. 确保uv包管理器已正确安装
3. 检查项目目录路径是否正确
4. 查看终端输出的错误信息

## 联系信息
- 项目作者: Fábio Ferreira (@fabiomlferreira)
- 项目地址: https://github.com/noopstudios/interactive-feedback-mcp
- 更多资源: https://dotcursorrules.com/
