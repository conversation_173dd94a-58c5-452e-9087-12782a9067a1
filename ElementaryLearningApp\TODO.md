# 小学伴读 - 开发任务清单 (TODO)

## Phase 1: 项目启动与核心架构 (MVP)

-   [ ] **项目初始化**
    -   [ ] 搭建Git仓库
    -   [ ] 确定技术栈并初始化Android项目 (e.g., Kotlin + Jetpack Compose)
    -   [ ] 设计并搭建后端基础架构 (用户认证、数据库模型)
-   [ ] **用户系统**
    -   [ ] 设计用户数据库表 (学生、家长)
    -   [ ] 实现注册、登录功能 (支持手机号/微信登录)
    -   [ ] 实现学生与家长账号绑定功能
-   [ ] **内容管理后台 (初步)**
    -   [ ] 设计内容数据库表，核心模型需包含 **年级(Grade)** 和 **教材版本(TextbookVersion)**，所有内容（课文、单词、题目）均与此关联。
    -   [ ] 开发一个简单的后台界面用于录入初始课程内容 (例如，一二年级语文)

## Phase 2: 核心学习功能开发 (学生端)

-   [ ] **教材选择功能**
    -   [x] 开发获取教材版本列表的后端API
    -   [x] 实现首次启动引导学生选择年级和教材版本的界面 (v1 - 列表展示)
    -   [ ] 开发个人中心，允许用户后续切换年级或教材版本
-   [ ] **语文模块**
    -   [x] 实现课文点读、跟读功能 (v1 - 列表与详情页完成)
    -   [x] 开发生字学习模块 (笔顺、发音、组词) (v1 - 列表完成)
-   [ ] **数学模块**
    -   [ ] 开发口算训练功能
    -   [ ] 实现一个知识点的互动练习 (e.g., 10以内加减法)
-   [ ] **英语模块**
    -   [ ] 开发单词卡片学习功能
-   [ ] **通用测试框架**
    -   [ ] 实现单选题、判断题题型的前端展示与交互
    -   [ ] 开发答题、提交、自动批改逻辑
    -   [ ] 实现简单的错题本功能 (自动记录错题)
-   [ ] **[阶段交付]** 生成包含核心学习功能的测试版APP，用于效果评审。

## Phase 3: 家长端与数据分析

-   [ ] **家长端**
    -   [ ] 开发学习报告界面，展示学生学习时长和正确率
    -   [ ] 实现查看绑定学生的错题本功能
-   [ ] **数据统计**
    -   [ ] 后端实现学习记录的数据埋点和统计
    -   [ ] 生成简单的日报/周报数据接口
-   [ ] **[阶段交付]** 生成包含初步家长端和数据报告的测试版APP，用于效果评审。

## Phase 4: 优化与扩展 (Post-MVP)

-   [ ] **完善学习内容**
    -   [ ] 逐步录入所有年级和科目的内容
    -   [ ] 引入古诗词、动画讲解等高级内容
-   [ ] **智能化功能**
    -   [ ] 设计并实现智能复习推送算法
    -   [ ] 开发专项练习模块
    -   [ ] 开发模拟考试功能
-   [ ] **趣味激励系统增强**
    -   [ ] 设计并实现“知识岛屿冒险”学习地图
    -   [ ] 开发“每日挑战”功能模块
    -   [ ] 设计并实现积分和徽章系统
-   [ ] **健康关怀功能**
    -   [ ] 开发护眼模式，支持主题切换和字体调整
    -   [ ] 实现定时休息提醒与锁定功能
-   [ ] **自主学习引导功能**
    -   [ ] 开发学习计划智能推荐引擎
    -   [ ] 实现学习目标设定与追踪模块
    -   [ ] 设计并开发知识图谱探索式学习界面
-   [ ] **UI/UX 优化**
    -   [ ] 全面优化APP界面和交互体验
    -   [ ] 增加动画和音效
-   [ ] **家长端增强**
    -   [ ] 开发学习计划设置功能
    -   [ ] 实现精细化的学习时长管理与远程锁定功能
-   [ ] **[阶段交付]** 生成功能完善的测试版APP，用于上线前的最终评审。

## Phase 5: 上线与迭代

-   [ ] **测试**
    -   [ ] 内部测试
    -   [ ] 小范围用户内测
-   [ ] **部署与发布**
    -   [ ] 服务器部署与性能优化
    -   [ ] 在各大安卓应用商店上架
-   [ ] **持续迭代**
    -   [ ] 根据用户反馈收集需求
    *   [ ] 增加AI老师问答等新功能
