<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2018 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem>

    <template name="tuple_ok_retval">
        %PYARG_0 = PyTuple_New(2);
        PyTuple_SetItem(%PYARG_0, 0, %CONVERTTOPYTHON[bool](ok_));
        PyTuple_SetItem(%PYARG_0, 1, %CONVERTTOPYTHON[%RETURN_TYPE](retval_));
    </template>

    <template name="bool*_fix,arg">
        bool ok_;
        %RETURN_TYPE retval_ = %CPPSELF.%FUNCTION_NAME(&amp;ok_, %2);
        <insert-template name="tuple_ok_retval"/>
    </template>

    <template name="bool*_fix,arg,arg,arg,arg">
        bool ok_;
        %RETURN_TYPE retval_ = %CPPSELF.%FUNCTION_NAME(&amp;ok_, %2, %3, %4, %5);
        <insert-template name="tuple_ok_retval"/>
    </template>

    <!-- Templates to fix bool* parameters -->
    <template name="tuple_retval_ok">
        %PYARG_0 = PyTuple_New(2);
        PyTuple_SetItem(%PYARG_0, 0, %CONVERTTOPYTHON[%RETURN_TYPE](retval_));
        PyTuple_SetItem(%PYARG_0, 1, %CONVERTTOPYTHON[bool](ok_));
    </template>

    <template name="fix_bool*">
        bool ok_;
        %RETURN_TYPE retval_ = %CPPSELF.%FUNCTION_NAME(&amp;ok_);
        <insert-template name="tuple_retval_ok"/>
    </template>

    <template name="fix_args,bool*">
        bool ok_;
        %RETURN_TYPE retval_ = %CPPSELF.%FUNCTION_NAME(%ARGUMENT_NAMES, &amp;ok_);
        <insert-template name="tuple_retval_ok"/>
    </template>

    <template name="fix_args,arg,bool*">
        bool ok_;
        %RETURN_TYPE retval_ = %CPPSELF.%FUNCTION_NAME(%1, &amp;ok_);
        <insert-template name="tuple_retval_ok"/>
    </template>

    <template name="fix_arg,bool*,arg">
        bool ok_;
        %RETURN_TYPE retval_ = %CPPSELF.%FUNCTION_NAME(%1, &amp;ok_, %3);
        <insert-template name="tuple_retval_ok"/>
    </template>

    <template name="fix_bool*,arg">
        bool ok_;
        %RETURN_TYPE retval_ = %CPPSELF.%FUNCTION_NAME(&amp;ok_, %2);
        <insert-template name="tuple_retval_ok"/>
    </template>

    <template name="fix_bool*,arg,arg">
        bool ok_;
        %RETURN_TYPE retval_ = %CPPSELF.%FUNCTION_NAME(&amp;ok_, %2, %3);
        <insert-template name="tuple_retval_ok"/>
    </template>

    <template name="fix_bool*,arg,arg,arg">
        bool ok_;
        %RETURN_TYPE retval_ = %CPPSELF.%FUNCTION_NAME(&amp;ok_, %2, %3, %4);
        <insert-template name="tuple_retval_ok"/>
    </template>

    <template name="fix_bool*,arg,arg,arg,arg">
        bool ok_;
        %RETURN_TYPE retval_ = %CPPSELF.%FUNCTION_NAME(&amp;ok_, %2, %3, %4, %5);
        <insert-template name="tuple_retval_ok"/>
    </template>

    <!-- QInputDialog: these should allow threads -->
    <template name="fix_arg,arg,arg,arg,arg,arg,arg,bool*,arg">
        bool ok_;
        %RETURN_TYPE retval_;
        Py_BEGIN_ALLOW_THREADS
        retval_ = %CPPSELF.%FUNCTION_NAME(%1, %2, %3, %4, %5, %6, %7, &amp;ok_, %9);
        Py_END_ALLOW_THREADS
        <insert-template name="tuple_retval_ok"/>
    </template>

    <template name="fix_arg,arg,arg,arg,arg,arg,arg,bool*,arg,arg">
        bool ok_;
        %RETURN_TYPE retval_;
        Py_BEGIN_ALLOW_THREADS
        retval_ = %CPPSELF.%FUNCTION_NAME(%1, %2, %3, %4, %5, %6, %7, &amp;ok_, %9, %10);
        Py_END_ALLOW_THREADS
        <insert-template name="tuple_retval_ok"/>
    </template>

    <template name="fix_arg,arg,arg,arg,arg,arg,bool*,arg">
        bool ok_;
        %RETURN_TYPE retval_;
        Py_BEGIN_ALLOW_THREADS
        retval_ = %CPPSELF.%FUNCTION_NAME(%1, %2, %3, %4, %5, %6, &amp;ok_, %8);
        Py_END_ALLOW_THREADS
        <insert-template name="tuple_retval_ok"/>
    </template>

    <template name="fix_arg,arg,arg,arg,arg,bool*,arg">
        bool ok_;
        %RETURN_TYPE retval_;
        Py_BEGIN_ALLOW_THREADS
        retval_ = %CPPSELF.%FUNCTION_NAME(%1, %2, %3, %4, %5, &amp;ok_, %7);
        Py_END_ALLOW_THREADS
        <insert-template name="tuple_retval_ok"/>
    </template>

    <template name="fix_arg,arg,arg,arg,bool*,arg,arg">
        bool ok_;
        %RETURN_TYPE retval_;
        Py_BEGIN_ALLOW_THREADS
        retval_ = %CPPSELF.%FUNCTION_NAME(%1, %2, %3, %4, &amp;ok_, %6, %7);
        Py_END_ALLOW_THREADS
        <insert-template name="tuple_retval_ok"/>
    </template>
    <!-- End of QInputDialog templates -->

    <template name="fix_char*">
        char val_{};
        %RETURN_TYPE retval_ = %CPPSELF.%FUNCTION_NAME(&amp;val_);
        %PYARG_0 = PyTuple_New(2);
        PyTuple_SetItem(%PYARG_0, 0, %CONVERTTOPYTHON[%RETURN_TYPE](retval_));
        PyTuple_SetItem(%PYARG_0, 1, %CONVERTTOPYTHON[char](val_));
    </template>

    <template name="tuple_abcd_same_type">
        %PYARG_0 = PyTuple_New(4);
        PyTuple_SetItem(%PYARG_0, 0, %CONVERTTOPYTHON[$TYPE](a));
        PyTuple_SetItem(%PYARG_0, 1, %CONVERTTOPYTHON[$TYPE](b));
        PyTuple_SetItem(%PYARG_0, 2, %CONVERTTOPYTHON[$TYPE](c));
        PyTuple_SetItem(%PYARG_0, 3, %CONVERTTOPYTHON[$TYPE](d));
    </template>

    <template name="fix_number*,number*,number*,number*">
        $TYPE a, b, c, d;
        %CPPSELF->::%TYPE::%FUNCTION_NAME(&amp;a, &amp;b, &amp;c, &amp;d);
        <insert-template name="tuple_abcd_same_type"/>
    </template>

    <template name="fix_number*,number*,number*,number*,args">
        $TYPE a, b, c, d;
        %CPPSELF->::%TYPE::%FUNCTION_NAME(&amp;a, &amp;b, &amp;c, &amp;d, %ARGUMENT_NAMES);
        <insert-template name="tuple_abcd_same_type"/>
    </template>

    <template name="fix_native_return_number*,number*,number*,number*">
        PyObject* _obj = %PYARG_0.object();
        Shiboken::AutoDecRef _obj0(PySequence_GetItem(_obj, 0));
        Shiboken::AutoDecRef _obj1(PySequence_GetItem(_obj, 1));
        Shiboken::AutoDecRef _obj2(PySequence_GetItem(_obj, 2));
        Shiboken::AutoDecRef _obj3(PySequence_GetItem(_obj, 3));
        if (!PySequence_Check(_obj)
            || PySequence_Size(_obj) != 4
            || !PyNumber_Check(_obj0)
            || !PyNumber_Check(_obj1)
            || !PyNumber_Check(_obj2)
            || !PyNumber_Check(_obj3)) {
            PyErr_SetString(PyExc_TypeError, "Sequence of 4 numbers expected");
        } else {
            *%1 = %CONVERTTOCPP[$TYPE](_obj0);
            *%2 = %CONVERTTOCPP[$TYPE](_obj1);
            *%3 = %CONVERTTOCPP[$TYPE](_obj2);
            *%4 = %CONVERTTOCPP[$TYPE](_obj3);
        }
    </template>

    <template name="fix_number*,number*,number*,number*,number*">
        $TYPE a, b, c, d, e;
        %CPPSELF.%FUNCTION_NAME(&amp;a, &amp;b, &amp;c, &amp;d, &amp;e);
        %PYARG_0 = PyTuple_New(5);
        PyTuple_SetItem(%PYARG_0, 0, %CONVERTTOPYTHON[$TYPE](a));
        PyTuple_SetItem(%PYARG_0, 1, %CONVERTTOPYTHON[$TYPE](b));
        PyTuple_SetItem(%PYARG_0, 2, %CONVERTTOPYTHON[$TYPE](c));
        PyTuple_SetItem(%PYARG_0, 3, %CONVERTTOPYTHON[$TYPE](d));
        PyTuple_SetItem(%PYARG_0, 4, %CONVERTTOPYTHON[$TYPE](e));
    </template>

    <template name="fix_args,number*,number*">
        $TYPE a, b;
        %CPPSELF.%FUNCTION_NAME(%ARGUMENT_NAMES, &amp;a, &amp;b);
        %PYARG_0 = PyTuple_New(2);
        PyTuple_SetItem(%PYARG_0, 0, %CONVERTTOPYTHON[$TYPE](a));
        PyTuple_SetItem(%PYARG_0, 1, %CONVERTTOPYTHON[$TYPE](b));
    </template>

    <template name="fix_arg,int*,int*">
    %RETURN_TYPE _ret;
    int a, b;
    _ret = %CPPSELF.%FUNCTION_NAME(%1, &amp;a, &amp;b);
    %PYARG_0 = PyTuple_New(3);
    PyTuple_SetItem(%PYARG_0, 0, %CONVERTTOPYTHON[%RETURN_TYPE](_ret));
    PyTuple_SetItem(%PYARG_0, 1, %CONVERTTOPYTHON[int](a));
    PyTuple_SetItem(%PYARG_0, 2, %CONVERTTOPYTHON[int](b));
    </template>

    <template name="return_tuple_QValidator_QString_int">
        %RETURN_TYPE retval_ = %RETURN_TYPE(%CPPSELF.%FUNCTION_NAME(%1, %2));
        %PYARG_0 = PyTuple_New(3);
        PyTuple_SetItem(%PYARG_0, 0, %CONVERTTOPYTHON[%RETURN_TYPE](retval_));
        PyTuple_SetItem(%PYARG_0, 1, %CONVERTTOPYTHON[%ARG1_TYPE](%1));
        PyTuple_SetItem(%PYARG_0, 2, %CONVERTTOPYTHON[%ARG2_TYPE](%2));
    </template>

    <template name="repr_code">
        QString format = QString::asprintf("%s(%REPR_FORMAT)",
            Py_TYPE(%PYSELF)->tp_name, %REPR_ARGS);
        %PYARG_0 = Shiboken::String::fromCString(qPrintable(format));
    </template>

    <template name="repr_qdebug">
        QString result;
        QDebug(&amp;result).nospace() &lt;&lt; "&lt;PySide6.MODULE." &lt;&lt; %CPPSELF &lt;&lt; '>';
        %PYARG_0 = Shiboken::String::fromCString(qPrintable(result));
    </template>

   <template name="repr_qdebug_gui">
       <insert-template name="repr_qdebug">
           <replace from="MODULE" to="QtGui"/>
       </insert-template>
   </template>

    <template name="return_internal_pointer">
        %PYARG_0 = reinterpret_cast&lt;PyObject*>(%CPPSELF.%FUNCTION_NAME());
        if (!%PYARG_0)
            %PYARG_0 = Py_None;
        Py_INCREF(%PYARG_0);
    </template>

    <!-- Helpers for modifying "bool nativeEventFilter(QByteArray, void*, long *result)"
         to return a tuple of bool,long -->
    <template name="return_native_eventfilter_conversion_variables">
        qintptr resultVar{0};
        qintptr *%out = &amp;resultVar;
    </template>
    <template name="return_native_eventfilter_conversion">
        %RETURN_TYPE %out = false;
        if (PySequence_Check(%PYARG_0) &amp;&amp; (PySequence_Size(%PYARG_0) == 2)) {
            Shiboken::AutoDecRef pyItem(PySequence_GetItem(%PYARG_0, 0));
            %out = %CONVERTTOCPP[bool](pyItem);
            if (result) {
                Shiboken::AutoDecRef pyResultItem(PySequence_GetItem(pyResult, 1));
                *result = %CONVERTTOCPP[long](pyResultItem);
            }
        }
    </template>

    <template name="return_native_eventfilter">
        %PYARG_0 = PyTuple_New(2);
        PyTuple_SetItem(%PYARG_0, 0, %CONVERTTOPYTHON[%RETURN_TYPE](%0));
        PyTuple_SetItem(%PYARG_0, 1, %CONVERTTOPYTHON[long](*result_out));
    </template>

    <!-- templates for __reduce__ -->
    <template name="reduce_code">
        %PYARG_0 = Py_BuildValue("(N(%REDUCE_FORMAT))", PyObject_Type(%PYSELF), %REDUCE_ARGS);
    </template>

    <!-- Replace '#' for the argument number you want. -->
    <template name="return_argument">
        Py_INCREF(%PYARG_#);
        %PYARG_0 = %PYARG_#;
    </template>

    <!-- Iterator -->
    <template name="__iter__">
        Py_INCREF(%PYSELF);
        %PYARG_0 = %PYSELF;
    </template>

    <template name="to_tuple">
        %PYARG_0 = Py_BuildValue("%TT_FORMAT", %TT_ARGS);
    </template>

    <template name="checkPyCapsuleOrPyCObject_func">
    static bool checkPyCapsuleOrPyCObject(PyObject* pyObj)
    {
        return PyCapsule_CheckExact(pyObj);
    }
    </template>

</typesystem>
