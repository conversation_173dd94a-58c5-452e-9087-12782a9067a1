<?xml version="1.0" encoding="UTF-8"?>
<!--
// Copyright (C) 2016 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
-->
<typesystem package="PySide6.QtWidgets"
            namespace-begin="QT_BEGIN_NAMESPACE" namespace-end="QT_END_NAMESPACE">
  <load-typesystem name="core_common.xml" generate="no"/>
  <load-typesystem name="widgets_common.xml" generate="no"/>



  <!-- Qt5: Beware forgetting the following rejection!
    There are anonymous enums "enum { Type = 1 }" etc. in these QGraphics classes, but they
    don't show up as "enum_1", but pretend they were a real enum field, not a value.

  """
  Generating class model...                    [WARNING]
    enum 'QGraphicsItem::UserType' does not have a type entry or is not an enum
    enum 'QGraphicsWidget::Type' does not have a type entry or is not an enum
    enum 'QGraphicsProxyWidget::Type' does not have a type entry or is not an enum
    enum 'QGraphicsEllipseItem::Type' does not have a type entry or is not an enum
    enum 'QGraphicsPixmapItem::Type' does not have a type entry or is not an enum
    enum 'QGraphicsSimpleTextItem::Type' does not have a type entry or is not an enum
    enum 'QGraphicsTextItem::Type' does not have a type entry or is not an enum
    enum 'QGraphicsItemGroup::Type' does not have a type entry or is not an enum
    enum 'QGraphicsRectItem::Type' does not have a type entry or is not an enum
    enum 'QGraphicsLineItem::Type' does not have a type entry or is not an enum
    enum 'QGraphicsPathItem::Type' does not have a type entry or is not an enum
    enum 'QGraphicsPolygonItem::Type' does not have a type entry or is not an enum
  """
  -->
  <rejection class="QMdi"/>

  <function signature="qDrawShadeLine(QPainter*,int,int,int,int,const QPalette&amp;,bool,int,int)"/>
  <function signature="qDrawShadeLine(QPainter*,const QPoint,const QPoint,const QPalette&amp;,bool,int,int)"/>
  <function signature="qDrawShadeRect(QPainter*,int,int,int,int,const QPalette&amp;,bool,int,int,const QBrush*)"/>
  <function signature="qDrawShadeRect(QPainter*,const QRect &amp;,const QPalette&amp;,bool,int,int,const QBrush*)"/>
  <function signature="qDrawShadePanel(QPainter*,int,int,int,int,const QPalette&amp;,bool,int,const QBrush*)"/>
  <function signature="qDrawShadePanel(QPainter*,const QRect&amp;,const QPalette&amp;,bool,int,const QBrush*)"/>
  <function signature="qDrawWinButton(QPainter*,int,int,int,int,const QPalette&amp;,bool,const QBrush*)"/>
  <function signature="qDrawWinButton(QPainter*,const QRect&amp;,const QPalette&amp;,bool,const QBrush*)"/>
  <function signature="qDrawWinPanel(QPainter*,int,int,int,int,const QPalette&amp;,bool,const QBrush*)"/>
  <function signature="qDrawWinPanel(QPainter*,const QRect&amp;,const QPalette&amp;,bool,const QBrush*)"/>
  <function signature="qDrawPlainRect(QPainter*,int,int,int,int,const QColor&amp;,int,const QBrush*)"/>
  <function signature="qDrawPlainRect(QPainter*,const QRect&amp;,const QColor&amp;,int,const QBrush*)"/>
  <function signature="qDrawPlainRoundedRect(QPainter*,int,int,int,int,qreal,qreal,const QColor&amp;,int,const QBrush*)" since="6.7"/>
  <function signature="qDrawPlainRoundedRect(QPainter*,const QRect&amp;,qreal,qreal,const QColor&amp;,int,const QBrush *)" since="6.7"/>

  <object-type name="QStyleOption" polymorphic-id-expression="%B-&gt;type == QStyleOption::SO_Default"
               polymorphic-name-function="styleOptionType">
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp"
                 snippet="qstyleoption-typename"/>
    <enum-type name="OptionType"/>
    <enum-type name="StyleOptionType"/>
    <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionGraphicsItem"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionGraphicsItem *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionSizeGrip"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionSizeGrip *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionButton"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionButton *&gt;(%B) != nullptr">
      <enum-type name="ButtonFeature" flags="ButtonFeatures"/>
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionComboBox"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionComboBox *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionComplex"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionComplex *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionDockWidget"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionDockWidget *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionFocusRect" polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionFocusRect *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionFrame"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionFrame *&gt;(%B) != nullptr">
      <enum-type name="FrameFeature" flags="FrameFeatures"/>
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionGroupBox"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionGroupBox *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionHeader"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionHeader *&gt;(%B) != nullptr">
      <enum-type name="SectionPosition"/>
      <enum-type name="SelectedPosition"/>
      <enum-type name="SortIndicator"/>
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionHeaderV2">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionMenuItem"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionMenuItem *&gt;(%B) != nullptr">
      <enum-type name="CheckType"/>
      <enum-type name="MenuItemType"/>
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionProgressBar"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionProgressBar *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionRubberBand"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionRubberBand *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionSlider"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionSlider *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionSpinBox"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionSpinBox *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionTab"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionTab *&gt;(%B) != nullptr">
      <enum-type name="CornerWidget" flags="CornerWidgets"/>
      <enum-type name="SelectedPosition"/>
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
      <enum-type name="TabFeature" flags="TabFeatures"/>
      <enum-type name="TabPosition"/>
  </object-type>
  <object-type name="QStyleOptionTabBarBase"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionTabBarBase *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionTabWidgetFrame"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionTabWidgetFrame *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionTitleBar" polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionTitleBar *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleOptionToolBar"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionToolBar *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
      <enum-type name="ToolBarFeature" flags="ToolBarFeatures"/>
      <enum-type name="ToolBarPosition"/>
  </object-type>
  <object-type name="QStyleOptionToolBox"
               polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionToolBox *&gt;(%B) != nullptr">
      <enum-type name="SelectedPosition"/>
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
      <enum-type name="TabPosition"/>
  </object-type>
  <object-type name="QStyleOptionToolButton" polymorphic-id-expression="qstyleoption_cast&lt;const QStyleOptionToolButton *&gt;(%B) != nullptr">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
      <enum-type name="ToolButtonFeature" flags="ToolButtonFeatures"/>
  </object-type>
  <value-type name="QStyleOptionViewItem"
              polymorphic-id-expression="%B-&gt;type == QStyleOptionViewItem::Type &amp;&amp; %B-&gt;version == QStyleOptionViewItem::Version">
      <enum-type name="Position"/>
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
      <enum-type name="ViewItemFeature" flags="ViewItemFeatures"/>
      <enum-type name="ViewItemPosition"/>
  </value-type>

  <object-type name="QAccessibleWidget"/>
  <value-type name="QColormap">
      <enum-type name="Mode"/>
  </value-type>
  <value-type name="QSizePolicy">
      <enum-type name="ControlType" flags="ControlTypes"/>
      <enum-type name="Policy"/>
      <enum-type name="PolicyFlag" python-type="IntFlag"/>
  </value-type>
  <value-type name="QTableWidgetSelectionRange"/>

  <value-type name="QTreeWidgetItemIterator" >
    <modify-function signature="QTreeWidgetItemIterator(QTreeWidget*,QFlags&lt;QTreeWidgetItemIterator::IteratorFlag&gt;)">
        <modify-argument index="this">
            <parent index="1" action="add"/>
        </modify-argument>
    </modify-function>

    <add-function signature="__iter__()" return-type="PyObject*">
      <inject-code class="target" position="beginning">
        <insert-template name="__iter__"/>
      </inject-code>
    </add-function>
    <add-function signature="__next__()" return-type="PyObject*">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qtreewidgetitemiterator-next"/>
    </add-function>

    <add-function signature="value()" return-type="QTreeWidgetItem*">
      <inject-code file="../glue/qtwidgets.cpp" snippet="qtreewidgetitemiterator-value"/>
    </add-function>
    <enum-type name="IteratorFlag" flags="IteratorFlags"/>
    <!-- ### See bug 778 -->
    <modify-function signature="operator++(int)" remove="all"/>
    <modify-function signature="operator--(int)" remove="all"/>
    <modify-function signature="operator++()" remove="all"/>
    <modify-function signature="operator--()" remove="all"/>
    <!-- ### Operator* doesn't make sense in Python. -->
    <modify-function signature="operator*()const" remove="all"/>
    <!-- ### -->
  </value-type>

  <object-type name="QLayoutItem">

    <modify-function signature="widget()const">
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="layout()">
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="spacerItem()">
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
  </object-type>

  <!-- Register Qt meta type only for base class QGraphicsItem as registering
       QGraphicsItemGroup* breaks QGraphicsItem::itemChange()), PYSIDE-1887 -->
  <object-type name="QGraphicsItem" qt-register-metatype="base" parent-management="true">
    <enum-type name="CacheMode"/>
    <enum-type name="Extension"/>
    <enum-type name="GraphicsItemChange"/>
    <enum-type name="GraphicsItemFlag" flags="GraphicsItemFlags"/>
    <enum-type name="PanelModality" since="4.6"/>
    <inject-code class="target" position="end" file="../glue/qtwidgets.cpp" snippet="qgraphicsitem"/>
    <modify-function signature="setParentItem(QGraphicsItem*)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="scene()const">
        <inject-code position="end" file="../glue/qtwidgets.cpp" snippet="qgraphicsitem-scene-return-parenting"/>
        <modify-argument index="this">
          <parent index="return" action="add"/>
        </modify-argument>
    </modify-function>

    <modify-function signature="parentItem()const">
      <modify-argument index="return">
        <define-ownership owner="target"/>
      </modify-argument>
      <modify-argument index="this">
        <parent index="return" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="parentWidget()const">
        <modify-argument index="this">
          <parent index="return" action="add"/>
        </modify-argument>
        <modify-argument index="return">
          <define-ownership class="target" owner="default"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="paint(QPainter*,const QStyleOptionGraphicsItem*,QWidget*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="collidesWithItem(const QGraphicsItem*,Qt::ItemSelectionMode)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="contextMenuEvent(QGraphicsSceneContextMenuEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="dragEnterEvent(QGraphicsSceneDragDropEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="dragLeaveEvent(QGraphicsSceneDragDropEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="dragMoveEvent(QGraphicsSceneDragDropEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="dropEvent(QGraphicsSceneDragDropEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="focusInEvent(QFocusEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="focusOutEvent(QFocusEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="hoverEnterEvent(QGraphicsSceneHoverEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="hoverLeaveEvent(QGraphicsSceneHoverEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="hoverMoveEvent(QGraphicsSceneHoverEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="inputMethodEvent(QInputMethodEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="isBlockedByModalPanel(QGraphicsItem**)const" since="4.6">
      <modify-argument index="1">
        <remove-argument/>
      </modify-argument>
      <modify-argument index="return" pyi-type="Tuple[bool, PySide6.QtWidgets.QGraphicsItem]">
        <replace-type modified-type="(retval, blockingPanel)"/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qgraphicsitem-isblockedbymodalpanel"/>
    </modify-function>
    <modify-function signature="itemTransform(const QGraphicsItem*,bool*)const">
        <modify-argument index="2">
            <remove-argument />
            <remove-default-expression />
        </modify-argument>
        <modify-argument index="return" pyi-type="Tuple[PySide6.QtGui.QTransform, bool]">
            <replace-type modified-type="(QTransform, bool ok)"/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="fix_args,bool*"/>
        </inject-code>
    </modify-function>
    <modify-function signature="isObscuredBy(const QGraphicsItem*)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="keyPressEvent(QKeyEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="keyReleaseEvent(QKeyEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="mouseDoubleClickEvent(QGraphicsSceneMouseEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="mouseMoveEvent(QGraphicsSceneMouseEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="mousePressEvent(QGraphicsSceneMouseEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="mouseReleaseEvent(QGraphicsSceneMouseEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="sceneEvent(QEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="sceneEventFilter(QGraphicsItem*,QEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
      <modify-argument index="2" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="wheelEvent(QGraphicsSceneWheelEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="setGraphicsEffect(QGraphicsEffect*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <!-- ### These methods are internal on Qt. -->
    <modify-function signature="supportsExtension(QGraphicsItem::Extension)const" remove="all"/>
    <modify-function signature="setExtension(QGraphicsItem::Extension,QVariant)" remove="all"/>
    <!-- ### -->
  </object-type>
  <object-type name="QAbstractGraphicsShapeItem"/>
  <object-type name="QAbstractItemView">
    <enum-type name="CursorAction"/>
    <enum-type name="DragDropMode"/>
    <enum-type name="DropIndicatorPosition"/>
    <enum-type name="EditTrigger" flags="EditTriggers"/>
    <enum-type name="ScrollHint"/>
    <enum-type name="ScrollMode"/>
    <enum-type name="SelectionBehavior"/>
    <enum-type name="SelectionMode"/>
    <enum-type name="State"/>
    <modify-function signature="setModel(QAbstractItemModel*)">
      <modify-argument index="1" pyi-type="Optional[PySide6.QtCore.QAbstractItemModel]">
        <reference-count action="set"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setSelectionModel(QItemSelectionModel*)">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItemDelegate(QAbstractItemDelegate*)">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItemDelegateForColumn(int,QAbstractItemDelegate*)">
      <modify-argument index="2">
        <reference-count action="set"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItemDelegateForRow(int,QAbstractItemDelegate*)">
      <modify-argument index="2">
        <reference-count action="set"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="model()const">
      <modify-argument index="return">
        <!-- Defining ownership as "default" avoids the object to be automatically
             set as parent of the returned pointer. -->
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="selectionModel()const">
      <modify-argument index="return">
        <!-- Defining ownership as "default" avoids the object to be automatically
             set as parent of the returned pointer. -->
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setIndexWidget(const QModelIndex &amp;,QWidget*)" allow-thread="yes">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="edit(QModelIndex,QAbstractItemView::EditTrigger,QEvent*)">
      <modify-argument index="3" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="selectionCommand(QModelIndex,const QEvent*)const">
      <modify-argument index="2" invalidate-after-use="yes"/>
    </modify-function>
  </object-type>
  <object-type name="QAbstractSlider">
    <enum-type name="SliderAction"/>
    <enum-type name="SliderChange"/>
  </object-type>
  <object-type name="QCheckBox"/>
  <object-type name="QCommonStyle">
  </object-type>
  <object-type name="QDataWidgetMapper">
    <enum-type name="SubmitPolicy"/>
    <modify-function signature="addMapping(QWidget*,int)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addMapping(QWidget*,int,QByteArray)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="removeMapping(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItemDelegate(QAbstractItemDelegate*)">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setModel(QAbstractItemModel*)">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QDateEdit"/>
  <object-type name="QDialog">
    <enum-type name="DialogCode" python-type="IntEnum"/>
    <modify-function signature="exec()" allow-thread="yes">
        <inject-code file="../glue/qtwidgets.cpp" snippet="qdialog-exec-remove-parent-relation"/>
    </modify-function>
    <add-function signature="exec_()" return-type="int">
        <inject-code file="../glue/qtwidgets.cpp" snippet="qapplication-exec"/>
    </add-function>
  </object-type>
  <object-type name="QDialogButtonBox">
    <enum-type name="ButtonLayout"/>
    <enum-type name="ButtonRole"/>
    <enum-type name="StandardButton" flags="StandardButtons"/>
    <modify-function signature="addButton(QAbstractButton*,QDialogButtonBox::ButtonRole)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="removeButton(QAbstractButton*)">
      <modify-argument index="1">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QFileIconProvider"/>
  <object-type name="QWizard">
    <enum-type name="WizardButton"/>
    <enum-type name="WizardOption" flags="WizardOptions"/>
    <enum-type name="WizardPixmap"/>
    <enum-type name="WizardStyle"/>
    <modify-function signature="addPage(QWizardPage*)">
      <modify-argument index="1">
        <no-null-pointer/>
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setPage(int,QWizardPage*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setButton(QWizard::WizardButton,QAbstractButton*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QWizardPage">
    <extra-includes>
        <include file-name="pysidesignal.h" location="global"/>
    </extra-includes>
    <modify-function signature="wizard()const">
      <modify-argument index="this">
        <parent index="return" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="registerField(const QString&amp;,QWidget*,const char*,const char*)" allow-thread="yes">
        <modify-argument index="3" pyi-type="str" rename="property"/>
        <modify-argument index="4" pyi-type="str" rename="changed_signal"/>
    </modify-function>
    <add-function signature="registerField(const QString&amp;@name@,QWidget*@widget@,const char*@property@,PySideSignalInstance@changedSignal@)">
        <modify-argument index="3" pyi-type="str"/>
            <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp"
                         snippet="qwizardpage-registerfield"/>
        </add-function>
  </object-type>
  <object-type name="QFocusFrame">
    <modify-function signature="setWidget(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QFontComboBox">
      <enum-type name="FontFilter" flags="FontFilters"/>
  </object-type>
  <object-type name="QFontDialog">
    <enum-type name="FontDialogOption" flags="FontDialogOptions"/>
    <modify-function signature="getFont(bool*,QWidget*)" allow-thread="yes">
        <modify-argument index="1">
            <remove-argument />
        </modify-argument>
        <modify-argument index="return" pyi-type="Tuple[bool, PySide6.QtGui.QFont]">
            <replace-type modified-type="PyTuple"/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="bool*_fix,arg"/>
        </inject-code>
    </modify-function>
    <modify-function signature="getFont(bool*,QFont,QWidget*,QString,QFlags&lt;QFontDialog::FontDialogOption&gt;)" allow-thread="yes">
        <modify-argument index="1">
            <remove-argument />
        </modify-argument>
        <modify-argument index="return" pyi-type="Tuple[bool, PySide6.QtGui.QFont]">
            <replace-type modified-type="PyTuple"/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="bool*_fix,arg,arg,arg,arg"/>
        </inject-code>
    </modify-function>
  </object-type>
  <object-type name="QGraphicsEllipseItem"/>
  <object-type name="QGraphicsItemAnimation">
    <modify-function signature="setItem(QGraphicsItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setTimeLine(QTimeLine*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <extra-includes>
      <include file-name="QPair" location="global"/>
    </extra-includes>
  </object-type>
  <object-type name="QGraphicsItemGroup">
    <modify-function signature="addToGroup(QGraphicsItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QGraphicsLineItem"/>
  <object-type name="QGraphicsPathItem"/>
  <object-type name="QGraphicsPixmapItem">
      <enum-type name="ShapeMode"/>
  </object-type>
  <object-type name="QGraphicsPolygonItem"/>
  <object-type name="QGraphicsRectItem"/>
  <object-type name="QGraphicsSimpleTextItem"/>
  <object-type name="QHBoxLayout"/>
  <object-type name="QHeaderView">
    <enum-type name="ResizeMode"/>
    <modify-function signature="paintSection(QPainter*,QRect,int)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
  </object-type>
  <object-type name="QItemDelegate">
    <!-- ### "doLayout(...)" is an internal method. -->
    <modify-function signature="doLayout(QStyleOptionViewItem,QRect*,QRect*,QRect*,bool)const" remove="all"/>
    <!-- ### -->
    <modify-function signature="drawCheck(QPainter*,QStyleOptionViewItem,QRect,Qt::CheckState)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="drawDecoration(QPainter*,QStyleOptionViewItem,QRect,QPixmap)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="drawDisplay(QPainter*,QStyleOptionViewItem,QRect,QString)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="drawFocus(QPainter*,QStyleOptionViewItem,QRect)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="setItemEditorFactory(QItemEditorFactory*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QItemEditorCreatorBase">
    <modify-function signature="createWidget(QWidget*)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
  </object-type>
  <object-type name="QItemEditorFactory">
      <modify-function signature="registerEditor(int,QItemEditorCreatorBase*)">
      <modify-argument index="2">
        <reference-count action="set"/>
      </modify-argument>
      <inject-code file="../glue/qtwidgets.cpp" snippet="qitemeditorfactory-registereditor"/>
    </modify-function>
    <modify-function signature="setDefaultFactory(QItemEditorFactory*)">
      <modify-argument index="1">
        <define-ownership owner="c++"/>
      </modify-argument>
      <inject-code file="../glue/qtwidgets.cpp" snippet="qitemeditorfactory-setdefaultfactory"/>
    </modify-function>
  </object-type>
  <object-type name="QListView">
      <enum-type name="Flow"/>
      <enum-type name="LayoutMode"/>
      <enum-type name="Movement"/>
      <enum-type name="ResizeMode"/>
      <enum-type name="ViewMode"/>
  </object-type>
  <object-type name="QColumnView">
    <modify-function signature="setPreviewWidget(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QMainWindow">
    <enum-type name="DockOption" flags="DockOptions"/>
    <modify-function signature="setCentralWidget(QWidget*)">
       <inject-code class="target" position="beginning">
         <insert-template name="replace_child">
           <replace from="$FUNCTION_GET_OLD" to="centralWidget"/>
           <replace from="$CHILD_TYPE" to="QWidget"/>
           <replace from="$PYARG" to="%PYARG_1"/>
           <replace from="$CPPARG" to="%1"/>
         </insert-template>
       </inject-code>
    </modify-function>

    <modify-function signature="setMenuBar(QMenuBar*)">
       <inject-code class="target" position="beginning">
         <insert-template name="replace_child">
           <replace from="$FUNCTION_GET_OLD" to="menuBar"/>
           <replace from="$CHILD_TYPE" to="QMenuBar"/>
           <replace from="$PYARG" to="%PYARG_1"/>
           <replace from="$CPPARG" to="%1"/>
         </insert-template>
       </inject-code>
   </modify-function>

   <modify-function signature="setMenuWidget(QWidget*)">
       <inject-code class="target" position="beginning">
         <insert-template name="replace_child">
           <replace from="$FUNCTION_GET_OLD" to="menuWidget"/>
           <replace from="$CHILD_TYPE" to="QWidget"/>
           <replace from="$PYARG" to="%PYARG_1"/>
           <replace from="$CPPARG" to="%1"/>
         </insert-template>
       </inject-code>
    </modify-function>

    <modify-function signature="setStatusBar(QStatusBar*)">
       <inject-code class="target" position="beginning">
         <insert-template name="replace_child">
           <replace from="$FUNCTION_GET_OLD" to="statusBar"/>
           <replace from="$CHILD_TYPE" to="QStatusBar"/>
           <replace from="$PYARG" to="%PYARG_1"/>
           <replace from="$CPPARG" to="%1"/>
         </insert-template>
       </inject-code>
   </modify-function>

   <modify-function signature="addDockWidget(Qt::DockWidgetArea,QDockWidget*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addDockWidget(Qt::DockWidgetArea,QDockWidget*,Qt::Orientation)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <!-- this fuction is declared when not defined QT_NO_TOOLBA -->
    <modify-function signature="addToolBar(Qt::ToolBarArea,QToolBar*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addToolBar(QToolBar*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addToolBar(const QString&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <!--- END QT_NO_TOOLBAR -->
    <modify-function signature="removeDockWidget(QDockWidget*)">
      <modify-argument index="1">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="removeToolBar(QToolBar*)">
      <modify-argument index="1">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="removeDockWidget(QDockWidget*)">
      <modify-argument index="2">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>

  </object-type>
  <object-type name="QMdiArea">
    <enum-type name="AreaOption" flags="AreaOptions"/>
    <enum-type name="ViewMode"/>
    <enum-type name="WindowOrder"/>
    <modify-function signature="addSubWindow(QWidget*,QFlags&lt;Qt::WindowType&gt;)" >
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="removeSubWindow(QWidget*)" allow-thread="yes"/>
  </object-type>
  <object-type name="QMdiSubWindow">
    <enum-type name="SubWindowOption" flags="SubWindowOptions"/>
    <modify-function signature="setWidget(QWidget*)" allow-thread="yes">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setSystemMenu(QMenu*)" allow-thread="yes">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QMenu">
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp" snippet="qmenu-glue"/>
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp" snippet="qwidget-addaction-glue"/>
    <!-- exec() -->
    <modify-function signature="exec()" allow-thread="yes"/>
    <add-function signature="exec_()" return-type="QAction*">
        <inject-code file="../glue/qtwidgets.cpp" snippet="qmenu-exec-1"/>
    </add-function>
    <!-- exec(QPoint, QAction) -->
    <modify-function signature="exec(const QPoint&amp;,QAction*)" allow-thread="yes"/>
    <add-function signature="exec_(const QPoint&amp;,QAction* @action@ = nullptr)" return-type="QAction*">
        <inject-code file="../glue/qtwidgets.cpp" snippet="qmenu-exec-2"/>
    </add-function>
    <!-- exec(QList<QPoint>, QPoint, QAction, QWidget) -->
    <modify-function signature="exec(QList&lt;QAction*>,const QPoint&amp;,QAction*,QWidget*)" allow-thread="yes"/>
    <add-function signature="exec_(QList&lt;QAction*>,const QPoint&amp;,QAction* @at@ = nullptr,QWidget* @parent@ = nullptr)" return-type="QAction*">
        <inject-code file="../glue/qtwidgets.cpp" snippet="qmenu-exec-3"/>
    </add-function>
    <modify-function signature="addMenu(QMenu*)">
      <modify-argument index="return">
        <parent index="1" action="add"/>
      </modify-argument>
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addMenu(const QString&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addMenu(const QIcon &amp;,const QString &amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertMenu(QAction*,QMenu*)">
      <modify-argument index="return">
        <parent index="2" action="add"/>
      </modify-argument>
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addMenu(const QString&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addMenu(const QIcon &amp;,const QString &amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <!-- ### "setNoReplayFor(QWidget*)" is an internal method. -->
    <modify-function signature="setNoReplayFor(QWidget*)" remove="all"/>

    <!-- FIXME PYSIDE7: Remove in favor of widgets methods -->
    <modify-function signature="addAction(const QString&amp;,const QObject*,const char*,const QKeySequence&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <add-function signature="addAction(QString&amp;@text@,PyObject*,QKeySequence&amp;@shortcut@)">
      <modify-argument index="3">
        <replace-default-expression with="0"/>
      </modify-argument>
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>

      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qmenu-addaction-1"/>
    </add-function>

    <add-function signature="addAction(QIcon&amp;,QString&amp;@text@,PyObject*,QKeySequence&amp;@shortcut@)">
      <modify-argument index="4">
        <replace-default-expression with="0"/>
      </modify-argument>
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>

      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qmenu-addaction-2"/>
    </add-function>
    <modify-function signature="addAction(const QIcon&amp;,const QString&amp;,const QObject*,const char*,const QKeySequence&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="clear()">
      <inject-code file="../glue/qtwidgets.cpp" snippet="qmenu-clear"/>
    </modify-function>

  </object-type>

  <object-type name="QMenuBar">
    <modify-function signature="addMenu(QMenu*)">
      <modify-argument index="return">
        <parent index="1" action="add"/>
      </modify-argument>
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addSeparator()">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertMenu(QAction*,QMenu*)">
      <modify-argument index="return">
        <parent index="2" action="add"/>
      </modify-argument>
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertSeparator(QAction*)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="clear()">
      <inject-code file="../glue/qtwidgets.cpp" snippet="qmenubar-clear"/>
    </modify-function>

  </object-type>
  <object-type name="QProgressBar">
      <enum-type name="Direction"/>
  </object-type>
  <object-type name="QProxyStyle">
      <modify-function signature="QProxyStyle(QStyle*)">
          <modify-argument index="1">
              <define-ownership owner="c++"/>
          </modify-argument>
      </modify-function>
  </object-type>
  <object-type name="QPushButton"/>
  <object-type name="QScrollArea">
    <modify-function signature="setWidget(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>

  <object-type name="QSpacerItem"/>
  <object-type name="QStatusBar">
    <modify-function signature="addWidget(QWidget*,int)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addPermanentWidget(QWidget*,int)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertWidget(int,QWidget*,int)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertPermanentWidget(int,QWidget*,int)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QStyleFactory">
    <modify-function signature="create(const QString&amp;)">
      <modify-argument index="return">
        <define-ownership owner="target"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QStyleHintReturn">
      <enum-type name="HintReturnType"/>
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleHintReturnVariant">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStyleHintReturnMask">
      <enum-type name="StyleOptionType"/>
      <enum-type name="StyleOptionVersion"/>
  </object-type>
  <object-type name="QStylePainter"/>
  <object-type name="QTableView">
    <modify-function signature="setHorizontalHeader(QHeaderView*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setVerticalHeader(QHeaderView*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QTimeEdit"/>
  <object-type name="QToolBox">
    <modify-function signature="addItem(QWidget*,const QIcon&amp;,const QString&amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addItem(QWidget*,const QString&amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertItem(int,QWidget*,const QIcon&amp;,const QString&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertItem(int,QWidget*,const QString&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="removeItem(int)">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qtoolbox-removeitem"/>
    </modify-function>
  </object-type>
  <object-type name="QToolButton">
    <enum-type name="ToolButtonPopupMode"/>
    <modify-function signature="setDefaultAction(QAction*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setMenu(QMenu*)">
    </modify-function>
  </object-type>
  <object-type name="QToolTip"/>
  <object-type name="QTreeView">
    <modify-function signature="drawBranches(QPainter*,QRect,QModelIndex)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="drawRow(QPainter*,QStyleOptionViewItem,QModelIndex)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="setHeader(QHeaderView*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QUndoView">
    <modify-function signature="setGroup(QUndoGroup*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setStack(QUndoStack*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="QUndoView(QUndoGroup*,QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="QUndoView(QUndoStack*,QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QVBoxLayout"/>

  <object-type name="QWhatsThis"/>
  <object-type name="QWidgetAction">
    <modify-function signature="setDefaultWidget(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="releaseWidget(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QWidgetItem" polymorphic-id-expression="%B-&gt;widget()"/>

  <object-type name="QGraphicsSceneContextMenuEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::GraphicsSceneContextMenu">
      <enum-type name="Reason"/>
  </object-type>
  <object-type name="QGraphicsSceneDragDropEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::GraphicsSceneDragEnter || %B-&gt;type() == QEvent::GraphicsSceneDragLeave || %B-&gt;type() == QEvent::GraphicsSceneDragMove || %B-&gt;type() == QEvent::GraphicsSceneDrop" >
    <!-- ### "setMimeData(const QMimeData*)" is an internal method. -->
    <modify-function signature="setMimeData(const QMimeData*)" remove="all"/>
    <!-- ### "setSource(QWidget*)" is an internal method. -->
    <modify-function signature="setSource(QWidget*)" remove="all"/>
    <!-- ### -->
  </object-type>
  <object-type name="QGraphicsSceneEvent" copyable="false" qt-register-metatype="base">
    <!-- ### "setWidget(QWidget*)" is an internal method. -->
    <modify-function signature="setWidget(QWidget*)" remove="all"/>
  </object-type>
  <object-type name="QGraphicsSceneMoveEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::GraphicsSceneMove"/>
  <object-type name="QGraphicsSceneResizeEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::GraphicsSceneResize"/>
  <object-type name="QGraphicsSceneHelpEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::GraphicsSceneHelp"/>
  <object-type name="QGraphicsSceneHoverEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::GraphicsSceneHoverEnter || %B-&gt;type() == QEvent::GraphicsSceneHoverLeave || %B-&gt;type() == QEvent::GraphicsSceneHoverMove"/>
  <object-type name="QGraphicsSceneMouseEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::GraphicsSceneMouseDoubleClick || %B-&gt;type() == QEvent::GraphicsSceneMouseMove || %B-&gt;type() == QEvent::GraphicsSceneMousePress || %B-&gt;type() == QEvent::GraphicsSceneMouseRelease"/>
  <object-type name="QGraphicsSceneWheelEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::GraphicsSceneWheel"/>

  <object-type name="QGestureEvent"
               polymorphic-id-expression="%B-&gt;type() == QEvent::Gesture || %B-&gt;type() == QEvent::GestureOverride" since="4.6">
    <modify-function signature="activeGestures()const">
      <modify-argument index="return">
        <define-ownership owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="canceledGestures()const">
      <modify-argument index="return">
        <define-ownership owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="gestures()const">
      <modify-argument index="return">
        <define-ownership owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="gesture(Qt::GestureType)const">
      <modify-argument index="return">
        <define-ownership owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="widget()const">
      <modify-argument index="return">
        <define-ownership owner="default"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QAbstractButton">
    <add-function signature="setShortcut(Qt::Key@key@)">
      <inject-code file="../glue/qtgui.cpp" snippet="set-qtkey-shortcut"/>
    </add-function>
  </object-type>
  <object-type name="QStyle">
    <enum-type name="ComplexControl" python-type="IntEnum"/>
    <enum-type name="ContentsType" python-type="IntEnum"/>
    <enum-type name="ControlElement" python-type="IntEnum"/>
    <enum-type name="PixelMetric" python-type="IntEnum"/>
    <enum-type name="PrimitiveElement" python-type="IntEnum"/>
    <enum-type name="RequestSoftwareInputPanel" since="4.6"/>
    <enum-type name="StandardPixmap" python-type="IntEnum"/>
    <enum-type name="StateFlag" flags="State"/>
    <enum-type name="StyleHint" python-type="IntEnum"/>
    <enum-type name="SubControl" flags="SubControls"/>
    <enum-type name="SubElement" python-type="IntEnum"/>
    <modify-function signature="drawComplexControl(QStyle::ComplexControl,const QStyleOptionComplex*,QPainter*,const QWidget*)const">
      <modify-argument index="3" invalidate-after-use="yes"/>
      <modify-argument index="4">
        <rename to="widget"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="drawControl(QStyle::ControlElement,const QStyleOption*,QPainter*,const QWidget*)const">
      <modify-argument index="3" invalidate-after-use="yes"/>
      <modify-argument index="4">
        <rename to="widget"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="drawPrimitive(QStyle::PrimitiveElement,const QStyleOption*,QPainter*,const QWidget*)const">
      <modify-argument index="3" invalidate-after-use="yes"/>
      <modify-argument index="4">
        <rename to="widget"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="hitTestComplexControl(QStyle::ComplexControl,const QStyleOptionComplex*,const QPoint&amp;,const QWidget*)const">
      <modify-argument index="4">
        <rename to="widget"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="styleHint(QStyle::StyleHint,const QStyleOption*,const QWidget*,QStyleHintReturn*)const">
      <modify-argument index="4" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="drawItemPixmap(QPainter*,QRect,int,QPixmap)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="drawItemText(QPainter*,QRect,int,QPalette,bool,QString,QPalette::ColorRole)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
  </object-type>
  <object-type name="QColorDialog">
    <enum-type name="ColorDialogOption" flags="ColorDialogOptions"/>
    <modify-function signature="getColor(const QColor&amp;,QWidget*,const QString&amp;,QFlags&lt;QColorDialog::ColorDialogOption>)" allow-thread="yes"/>
  </object-type>

  <object-type name="QLayout" polymorphic-base="true">
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp"
                 snippet="qwidget-retrieveobjectname"/>
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp" snippet="qlayout-help-functions"/>
    <enum-type name="SizeConstraint"/>

    <modify-function signature="itemAt(int)const">
      <modify-argument index="return">
        <define-ownership owner="default"/>
      </modify-argument>
      <inject-code class="target" position="end" file="../glue/qtwidgets.cpp"
                   snippet="addownership-item-at"/>
    </modify-function>

    <modify-function signature="removeWidget(QWidget*)">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="removeownership-1"/>
    </modify-function>
    <modify-function signature="removeItem(QLayoutItem*)">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="removeownership-1"/>
    </modify-function>

    <modify-function signature="replaceWidget(QWidget*,QWidget*,QFlags&lt;Qt::FindChildOption&gt;)">
       <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-2"/>
    </modify-function>

    <modify-function signature="parentWidget()const">
      <modify-argument index="this">
        <parent index="return" action="add"/>
      </modify-argument>
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="takeAt(int)">
      <modify-argument index="return">
        <parent index="this" action="remove"/>
        <define-ownership class="native" owner="c++"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="addItem(QLayoutItem*)">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>

    <modify-function signature="addWidget(QWidget*)" allow-thread="yes">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>
    <modify-function signature="addChildWidget(QWidget*)" allow-thread="yes">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>
    <modify-function signature="addChildLayout(QLayout*)">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>
    <modify-function signature="setMenuBar(QWidget*)" allow-thread="yes">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>

    <modify-function signature="getContentsMargins(int*,int*,int*,int*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*">
                <replace from="$TYPE" to="int"/>
            </insert-template>
        </inject-code>
        <inject-code class="native" position="end">
            <insert-template name="fix_native_return_number*,number*,number*,number*">
                <replace from="$TYPE" to="int"/>
            </insert-template>
        </inject-code>
    </modify-function>

    <add-function signature="setAlignment(QFlags&lt;Qt::AlignmentFlag&gt;@alignment@)">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qlayout-setalignment"/>
    </add-function>

  </object-type>

  <object-type name="QStackedLayout">
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp"
                 snippet="qwidget-retrieveobjectname"/>
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp" snippet="qlayout-help-functions"/>
    <enum-type name="StackingMode"/>
    <modify-function signature="insertWidget(int,QWidget*)" allow-thread="yes">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-2"/>
    </modify-function>
    <modify-function signature="addWidget(QWidget*)" allow-thread="yes">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>
  </object-type>

  <object-type name="QBoxLayout">
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp"
                 snippet="qwidget-retrieveobjectname"/>
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp" snippet="qlayout-help-functions"/>

    <enum-type name="Direction"/>

    <modify-function signature="addWidget(QWidget*,int,QFlags&lt;Qt::AlignmentFlag&gt;)" allow-thread="yes">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>

    <modify-function signature="addLayout(QLayout*,int)">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>

    <modify-function signature="insertWidget(int,QWidget*,int,QFlags&lt;Qt::AlignmentFlag&gt;)" allow-thread="yes">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-2"/>
    </modify-function>
    <modify-function signature="insertLayout(int,QLayout*,int)">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-2"/>
    </modify-function>
    <modify-function signature="insertItem(int,QLayoutItem*)">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-2"/>
    </modify-function>
    <modify-function signature="addSpacerItem(QSpacerItem*)">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>
    <modify-function signature="insertSpacerItem(int,QSpacerItem*)">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-2"/>
    </modify-function>
  </object-type>

  <object-type name="QGridLayout">
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp"
                 snippet="qwidget-retrieveobjectname"/>
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp" snippet="qlayout-help-functions"/>
    <modify-function signature="itemAtPosition (int,int)const">
      <modify-argument index="return">
        <define-ownership owner="default"/>
      </modify-argument>
      <inject-code class="target" position="end" file="../glue/qtwidgets.cpp"
                   snippet="addownership-item-at"/>
    </modify-function>
    <modify-function signature="addWidget(QWidget*,int,int,QFlags&lt;Qt::AlignmentFlag&gt;)">
      <modify-argument index="4">
        <rename to="alignment"/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>
    <modify-function signature="addWidget(QWidget*,int,int,int,int,QFlags&lt;Qt::AlignmentFlag&gt;)">
      <modify-argument index="6">
        <rename to="alignment"/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>
    <modify-function signature="addLayout(QLayout*,int,int,QFlags&lt;Qt::AlignmentFlag&gt;)">
      <modify-argument index="4">
        <rename to="alignment"/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>
    <modify-function signature="addLayout(QLayout*,int,int,int,int,QFlags&lt;Qt::AlignmentFlag&gt;)">
      <modify-argument index="6">
        <rename to="alignment"/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>
    <modify-function signature="addItem(QLayoutItem*,int,int,int,int,QFlags&lt;Qt::AlignmentFlag&gt;)">
      <modify-argument index="4">
        <rename to="rowSpan"/>
      </modify-argument>
      <modify-argument index="5">
        <rename to="columnSpan"/>
      </modify-argument>
      <modify-argument index="6">
        <rename to="alignment"/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="addownership-1"/>
    </modify-function>
    <modify-function signature="getItemPosition(int,int*,int*,int*,int*)const">
        <modify-argument index="return">
            <replace-type modified-type="PyObject*"/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="5">
            <remove-argument/>
            <remove-default-expression/>
        </modify-argument>
        <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qgridlayout-getitemposition"/>
    </modify-function>
  </object-type>

  <object-type name="QGraphicsView">
    <extra-includes>
      <include file-name="QPainterPath" location="global"/>
      <include file-name="QVarLengthArray" location="global"/>
    </extra-includes>
    <enum-type name="CacheModeFlag" flags="CacheMode"/>
    <enum-type name="DragMode"/>
    <enum-type name="OptimizationFlag" flags="OptimizationFlags"/>
    <enum-type name="ViewportAnchor"/>
    <enum-type name="ViewportUpdateMode"/>
    <modify-function signature="setScene(QGraphicsScene*)">
      <modify-argument index="1" pyi-type="Optional[PySide6.QtWidgets.QGraphicsScene]">
        <reference-count action="set"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="scene()const">
        <inject-code position="end" file="../glue/qtwidgets.cpp" snippet="qgraphicsitem-scene-return-parenting"/>
        <modify-argument index="return">
            <define-ownership owner="default"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="itemAt(int,int)const">
        <modify-argument index="return">
            <define-ownership owner="default"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="itemAt(QPoint)const">
        <modify-argument index="return">
            <define-ownership owner="default"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="drawBackground(QPainter*,QRectF)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="drawForeground(QPainter*,QRectF)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>

    <!-- TODO: Support conversions on virtual function -->
    <modify-function signature="drawItems(QPainter*,int,QGraphicsItem*[],const QStyleOptionGraphicsItem[])">
        <modify-argument index="2">
            <remove-argument/>
            <conversion-rule class="native">
                <insert-template name="pysequencesize_int"/>
            </conversion-rule>
        </modify-argument>

        <modify-argument index="3">
            <replace-type modified-type="PySequence"/>
            <conversion-rule class="native">
                <insert-template name="qgraphicsitem_pysequence"/>
            </conversion-rule>

            <conversion-rule class="target">
                <insert-template name="qgraphicsitem_pyobject"/>
            </conversion-rule>
        </modify-argument>

        <modify-argument index="4">
            <replace-type modified-type="PySequence"/>
            <conversion-rule class="target">
                <insert-template name="qstyleoptiongraphicsitem_pyobject"/>
            </conversion-rule>

            <conversion-rule class="native">
                <insert-template name="pysequence_qstyleoptiongraphicsitem"/>
            </conversion-rule>
        </modify-argument>
    </modify-function>
  </object-type>

  <object-type name="QInputDialog">
    <enum-type name="InputDialogOption"/>
    <enum-type name="InputMode"/>

    <modify-function signature="getInt(QWidget*,const QString&amp;,const QString&amp;,int,int,int,int,bool*,QFlags&lt;Qt::WindowType&gt;)" allow-thread="yes">
      <modify-argument index="return" pyi-type="Tuple[int, bool]"/>
      <modify-argument index="8">
        <remove-default-expression/>
        <remove-argument/>
      </modify-argument>
      <inject-code class="target" position="beginning">
        <insert-template name="fix_arg,arg,arg,arg,arg,arg,arg,bool*,arg"/>
      </inject-code>
    </modify-function>

    <modify-function signature="getItem(QWidget*,const QString&amp;,const QString&amp;,const QStringList&amp;,int,bool,bool*,QFlags&lt;Qt::WindowType&gt;,QFlags&lt;Qt::InputMethodHint&gt;)" allow-thread="yes">
      <modify-argument index="return" pyi-type="Tuple[str, bool]"/>
      <modify-argument index="7">
        <remove-default-expression/>
        <remove-argument/>
      </modify-argument>
      <inject-code class="target" position="beginning">
        <insert-template name="fix_arg,arg,arg,arg,arg,arg,bool*,arg"/>
      </inject-code>
    </modify-function>

    <modify-function signature="getMultiLineText(QWidget*,const QString&amp;,const QString&amp;,const QString&amp;,bool*,QFlags&lt;Qt::WindowType&gt;,QFlags&lt;Qt::InputMethodHint&gt;)" allow-thread="yes">
      <modify-argument index="return" pyi-type="Tuple[str, bool]"/>
      <modify-argument index="5">
        <remove-default-expression/>
        <remove-argument/>
      </modify-argument>
      <inject-code class="target" position="beginning">
        <insert-template name="fix_arg,arg,arg,arg,bool*,arg,arg"/>
      </inject-code>
    </modify-function>

    <modify-function signature="getText(QWidget*,const QString&amp;,const QString&amp;,QLineEdit::EchoMode,const QString&amp;,bool*,QFlags&lt;Qt::WindowType&gt;,QFlags&lt;Qt::InputMethodHint&gt;)" allow-thread="yes">
      <modify-argument index="return" pyi-type="Tuple[str, bool]"/>
      <modify-argument index="6">
        <remove-default-expression/>
        <remove-argument/>
      </modify-argument>
      <inject-code class="target" position="beginning">
        <insert-template name="fix_arg,arg,arg,arg,arg,bool*,arg"/>
      </inject-code>
    </modify-function>

    <modify-function signature="getDouble(QWidget*,const QString&amp;,const QString&amp;,double,double,double,int,bool*,QFlags&lt;Qt::WindowType&gt;,double)" allow-thread="yes">
      <modify-argument index="return" pyi-type="Tuple[float, bool]"/>
      <modify-argument index="8">
        <remove-default-expression/>
        <remove-argument/>
      </modify-argument>
      <inject-code class="target" position="beginning">
        <insert-template name="fix_arg,arg,arg,arg,arg,arg,arg,bool*,arg,arg"/>
      </inject-code>
    </modify-function>
  </object-type>

  <object-type name="QGraphicsScene">
    <extra-includes>
      <include file-name="QVarLengthArray" location="global"/>
    </extra-includes>
    <enum-type name="ItemIndexMethod"/>
    <enum-type name="SceneLayer" flags="SceneLayers"/>

    <!-- Qt5: note: this was called 'obsolete'. Is that true? -->
    <modify-function signature="drawItems(QPainter*,int,QGraphicsItem*[],const QStyleOptionGraphicsItem[],QWidget*)" remove="all"/>

    <modify-function signature="createItemGroup(const QList&lt;QGraphicsItem*&gt;&amp;)">
        <modify-argument index="1">
            <parent index="return" action="add"/>
        </modify-argument>
        <modify-argument index="return">
            <define-ownership owner="default"/>
        </modify-argument>
    </modify-function>

    <modify-function signature="destroyItemGroup(QGraphicsItemGroup*)">
        <inject-code file="../glue/qtwidgets.cpp" snippet="qgraphicsscene-destroyitemgroup"/>
    </modify-function>

    <modify-function signature="contextMenuEvent(QGraphicsSceneContextMenuEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="dragEnterEvent(QGraphicsSceneDragDropEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="dragLeaveEvent(QGraphicsSceneDragDropEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="dragMoveEvent(QGraphicsSceneDragDropEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="drawBackground(QPainter*,QRectF)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="drawForeground(QPainter*,QRectF)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="dropEvent(QGraphicsSceneDragDropEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="focusInEvent(QFocusEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="focusOutEvent(QFocusEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="helpEvent(QGraphicsSceneHelpEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="inputMethodEvent(QInputMethodEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="keyPressEvent(QKeyEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="keyReleaseEvent(QKeyEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="mouseDoubleClickEvent(QGraphicsSceneMouseEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="mouseMoveEvent(QGraphicsSceneMouseEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="mousePressEvent(QGraphicsSceneMouseEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="mouseReleaseEvent(QGraphicsSceneMouseEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="wheelEvent(QGraphicsSceneWheelEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="addItem(QGraphicsItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="addEllipse(const QRectF&amp;,const QPen&amp;,const QBrush&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addEllipse(qreal,qreal,qreal,qreal,const QPen&amp;,const QBrush&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="addLine(const QLineF&amp;,const QPen&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addLine(qreal,qreal,qreal,qreal,const QPen&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="addPath(const QPainterPath&amp;,const QPen&amp;,const QBrush&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addPixmap(const QPixmap&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addPolygon(const QPolygonF&amp;,const QPen&amp;,const QBrush&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addRect(const QRectF&amp;,const QPen&amp;,const QBrush&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addRect(qreal,qreal,qreal,qreal,const QPen&amp;,const QBrush&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="addText(const QString&amp;,const QFont&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addSimpleText(const QString&amp;,const QFont&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="addWidget(QWidget*,QFlags&lt;Qt::WindowType&gt;)">
        <!-- TODO: Add a keeper attribute to reference-count tag to do what this inject code do. -->
        <inject-code file="../glue/qtwidgets.cpp" snippet="qgraphicsscene-addwidget"/>
    </modify-function>

    <modify-function signature="clear()">
        <inject-code file="../glue/qtwidgets.cpp" snippet="qgraphicsscene-clear"/>
    </modify-function>

    <modify-function signature="removeItem(QGraphicsItem*)">
      <modify-argument index="1">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="setFocusItem(QGraphicsItem*,Qt::FocusReason)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>

  <object-type name="QCalendarWidget">
    <enum-type name="HorizontalHeaderFormat"/>
    <enum-type name="SelectionMode"/>
    <enum-type name="VerticalHeaderFormat"/>
    <extra-includes>
      <include file-name="QTextCharFormat" location="global"/>
    </extra-includes>
    <modify-function signature="paintCell(QPainter*,QRect,QDate)const">
      <modify-argument invalidate-after-use="yes" index="1"/>
    </modify-function>
  </object-type>
  <object-type name="QTreeWidget">
    <modify-function signature="mimeData(const QList&lt;QTreeWidgetItem*&gt;&amp;)const">
      <modify-argument index="return">
        <define-ownership class="native" owner="c++"/>
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItemWidget(QTreeWidgetItem*,int,QWidget*)" allow-thread="yes">
      <modify-argument index="3">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="dropMimeData(QTreeWidgetItem*,int,const QMimeData*,Qt::DropAction)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="addTopLevelItem(QTreeWidgetItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addTopLevelItems(const QList&lt;QTreeWidgetItem*&gt; &amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addTopLevelItem(QTreeWidgetItem*)">
      <modify-argument index="1">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertTopLevelItem(int,QTreeWidgetItem*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertTopLevelItems(int,const QList&lt;QTreeWidgetItem*&gt; &amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setHeaderItem(QTreeWidgetItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="takeTopLevelItem(int)">
      <modify-argument index="return">
        <define-ownership owner="default"/>
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="clear()">
        <inject-code file="../glue/qtwidgets.cpp" snippet="qtreewidget-clear"/>
    </modify-function>
    <modify-function signature="removeItemWidget(QTreeWidgetItem*,int)" allow-thread="yes"/>
  </object-type>
  <object-type name="QAbstractItemDelegate">
    <enum-type name="EndEditHint"/>
    <modify-function signature="paint(QPainter*,QStyleOptionViewItem,QModelIndex)const">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="editorEvent(QEvent*,QAbstractItemModel*,QStyleOptionViewItem,QModelIndex)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="createEditor(QWidget*,QStyleOptionViewItem,QModelIndex)const">
      <modify-argument index="1">
        <define-ownership owner="c++"/>
      </modify-argument>
      <modify-argument index="return">
        <define-ownership class="native" owner="c++"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="closeEditor(QWidget*,QAbstractItemDelegate::EndEditHint)" allow-thread="yes"/>
  </object-type>
  <object-type name="QTableWidgetItem" >
    <enum-type name="ItemType" python-type="IntEnum"/>
    <modify-function signature="read(QDataStream&amp;)" allow-thread="yes">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="write(QDataStream&amp;)const" allow-thread="yes">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
  </object-type>
  <object-type name="QListWidgetItem" >
    <enum-type name="ItemType" python-type="IntEnum"/>
    <modify-function signature="QListWidgetItem(const QString&amp;,QListWidget*,int)">
      <modify-argument index="this">
        <parent index="2" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="QListWidgetItem(const QIcon&amp;,const QString&amp;,QListWidget*,int)">
      <modify-argument index="this">
        <parent index="3" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="QListWidgetItem(QListWidget*,int)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="listWidget()const">
      <modify-argument index="return">
        <define-ownership owner="target"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="read(QDataStream&amp;)" allow-thread="yes">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="write(QDataStream&amp;)const" allow-thread="yes">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
  </object-type>
  <object-type name="QGraphicsTextItem">
    <!-- a QObject so main-thread delete redundant -->
    <extra-includes>
      <include file-name="QTextCursor" location="global"/>
    </extra-includes>
    <modify-function signature="setDocument(QTextDocument*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QCompleter">
    <enum-type name="CompletionMode"/>
    <enum-type name="ModelSorting"/>
    <modify-function signature="setModel(QAbstractItemModel*)">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setPopup(QAbstractItemView*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setWidget(QWidget*)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QTreeWidgetItem" hash-function="qHash" parent-management="true">
    <enum-type name="ChildIndicatorPolicy"/>
    <enum-type name="ItemType" python-type="IntEnum"/>
    <modify-function signature="read(QDataStream&amp;)" allow-thread="yes">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="write(QDataStream&amp;)const" allow-thread="yes">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="QTreeWidgetItem(QTreeWidget*,int)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="QTreeWidgetItem(QTreeWidget*,const QStringList&amp;,int)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="QTreeWidgetItem(QTreeWidget*,QTreeWidgetItem*,int)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="QTreeWidgetItem(QTreeWidgetItem*,int)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="QTreeWidgetItem(QTreeWidgetItem*,const QStringList &amp;,int)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="QTreeWidgetItem(QTreeWidgetItem*,QTreeWidgetItem*,int)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addChild(QTreeWidgetItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addChildren(const QList&lt;QTreeWidgetItem*&gt; &amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertChild(int,QTreeWidgetItem*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertChildren(int,const QList&lt;QTreeWidgetItem*&gt; &amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="removeChild(QTreeWidgetItem*)">
      <modify-argument index="1">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="takeChild(int)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="takeChildren()">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="parent()const">
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
      <inject-code class="target" position="end" file="../glue/qtwidgets.cpp" snippet="qtreewidgetitem"/>
    </modify-function>
    <modify-function signature="treeWidget()const">
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
      <inject-code class="target" position="end" file="../glue/qtwidgets.cpp" snippet="qtreewidgetitem"/>
    </modify-function>

  </object-type>
  <object-type name="QListWidget">
    <modify-function signature="mimeData(const QList&lt;QListWidgetItem*&gt;&amp;)const">
      <modify-argument index="return">
        <define-ownership class="native" owner="c++"/>
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItemWidget(QListWidgetItem*,QWidget*)" allow-thread="yes">
      <modify-argument index="2">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addItem(QListWidgetItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertItem(int,QListWidgetItem*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="takeItem(int)">
      <modify-argument index="return">
        <define-ownership owner="default"/>
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="clear()">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qlistwidget-clear"/>
    </modify-function>
    <modify-function signature="removeItemWidget(QListWidgetItem*)" allow-thread="yes"/>
  </object-type>

  <object-type name="QWidget" delete-in-main-thread="true" polymorphic-base="true">
    <!-- see QWindow::nativeEvent(), QAbstractNativeEventFilter::nativeEventFilter() -->
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp" snippet="qwidget-addaction-glue"/>
    <inject-code class="native" position="beginning">
    #include &lt;QtWidgets/qapplication.h&gt;
    </inject-code>
    <modify-function signature="nativeEvent(const QByteArray &amp;,void*,qintptr*)">
      <modify-argument index="3">
        <remove-argument/>
        <conversion-rule class="native">
            <insert-template name="return_native_eventfilter_conversion_variables"/>
        </conversion-rule>
      </modify-argument>
      <modify-argument index="return">
        <replace-type modified-type="PyObject"/>
        <conversion-rule class="native">
            <insert-template name="return_native_eventfilter_conversion"/>
        </conversion-rule>
      </modify-argument>
      <inject-code position="end">
          <insert-template name="return_native_eventfilter"/>
      </inject-code>
    </modify-function>

    <extra-includes>
      <include file-name="QApplication" location="global"/>
      <include file-name="QIcon" location="global"/>
      <include file-name="QMessageBox" location="global"/>
      <include file-name="QStyle" location="global"/>
    </extra-includes>

    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp"
                 snippet="qwidget-retrieveobjectname"/>
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp" snippet="qwidget-glue"/>

    <enum-type name="RenderFlag" flags="RenderFlags"/>

    <modify-function signature="setParent(QWidget*)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
      <modify-argument index="1" pyi-type="Optional[PySide6.QtWidgets.QWidget]"/>
    </modify-function>

    <modify-function signature="setParent(QWidget*,QFlags&lt;Qt::WindowType&gt;)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
      <modify-argument index="1" pyi-type="Optional[PySide6.QtWidgets.QWidget]"/>
    </modify-function>

    <modify-function signature="parentWidget()const">
        <modify-argument index="return">
          <define-ownership class="target" owner="default"/>
        </modify-argument>
    </modify-function>

    <modify-function signature="nativeParentWidget()const">  <!-- Suppress return value heuristics -->
        <modify-argument index="return">
          <define-ownership class="target" owner="default"/>
        </modify-argument>
    </modify-function>

    <modify-function signature="actionEvent(QActionEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="changeEvent(QEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="closeEvent(QCloseEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="contextMenuEvent(QContextMenuEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="dragEnterEvent(QDragEnterEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="dragLeaveEvent(QDragLeaveEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="dragMoveEvent(QDragMoveEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="dropEvent(QDropEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="enterEvent(QEnterEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="focusInEvent(QFocusEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="focusOutEvent(QFocusEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="hideEvent(QHideEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="inputMethodEvent(QInputMethodEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="keyPressEvent(QKeyEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="keyReleaseEvent(QKeyEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="leaveEvent(QEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="mouseDoubleClickEvent(QMouseEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="mouseMoveEvent(QMouseEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="mousePressEvent(QMouseEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="mouseReleaseEvent(QMouseEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="moveEvent(QMoveEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="paintEvent(QPaintEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="resizeEvent(QResizeEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="showEvent(QShowEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="tabletEvent(QTabletEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>
    <modify-function signature="wheelEvent(QWheelEvent*)">
        <modify-argument index="1" invalidate-after-use="yes">
            <rename to="event"/>
        </modify-argument>
    </modify-function>

    <modify-function signature="setStyle(QStyle*)">
      <inject-code class="target" position="end" file="../glue/qtwidgets.cpp" snippet="qwidget-setstyle"/>
    </modify-function>
    <modify-function signature="style()const">
      <inject-code class="target" position="end" file="../glue/qtwidgets.cpp" snippet="qwidget-style"/>
      <modify-argument index="return">
        <define-ownership owner="default"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="render(QPainter*,QPoint,QRegion,QFlags&lt;QWidget::RenderFlag&gt;)">
      <modify-argument index="2">
        <!-- Removed because the render(QPainter*) overload conflicts with the identical function in QGraphicsView -->
        <remove-default-expression/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setFocusProxy(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="addAction(const QString&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addAction(const QIcon&amp;,const QString&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addAction(const QString&amp;,const QKeySequence&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addAction(const QIcon&amp;,const QString&amp;,const QKeySequence&amp;)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="addAction(const QString&amp;,const QObject*,const char*,Qt::ConnectionType)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <add-function signature="addAction(QString&amp;@text@,PyObject*@callable@)"
                  return-type="QAction*">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>

      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qwidget-addaction-2"/>
    </add-function>

    <modify-function signature="addAction(const QIcon&amp;,const QString&amp;,const QObject*,const char*,Qt::ConnectionType)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <add-function signature="addAction(QIcon&amp;@icon@,QString&amp;@text@,PyObject*@callable@)"
                  return-type="QAction*">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>

      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qwidget-addaction-3"/>
    </add-function>

    <modify-function signature="addAction(const QString&amp;,const QKeySequence&amp;,const QObject*,const char*,Qt::ConnectionType)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <add-function signature="addAction(QString&amp;@text@,QKeySequence&amp;@shortcut@,PyObject*@callable@)"
                  return-type="QAction*">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>

      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qwidget-addaction-3"/>
    </add-function>

    <modify-function signature="addAction(const QIcon&amp;,const QString&amp;,const QKeySequence&amp;,const QObject*,const char*,Qt::ConnectionType)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <add-function signature="addAction(QIcon&amp;@icon@,QString&amp;@text@,QKeySequence&amp;@shortcut@,PyObject*@callable@)"
                  return-type="QAction*">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qwidget-addaction-4"/>
    </add-function>

    <modify-function signature="insertAction(QAction*,QAction*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="setLayout(QLayout*)" allow-thread="yes">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qwidget-setlayout"/>
    </modify-function>
    <modify-function signature="raise()" rename="raise_"/>
    <modify-function signature="setParent(QWidget*,QFlags&lt;Qt::WindowType>)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
      <modify-argument index="1" pyi-type="Optional[PySide6.QtWidgets.QWidget]"/>
    </modify-function>

    <modify-function signature="window()const">
      <modify-argument index="return">
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
  </object-type>

  <object-type name="QMessageBox">
    <enum-type name="ButtonRole"/>
    <enum-type name="Icon"/>
    <enum-type name="StandardButton" python-type="IntFlag" flags="StandardButtons"/>
    <enum-type name="Option" flags="Options" since="6.6"/>
    <modify-function signature="removeButton(QAbstractButton*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <extra-includes>
      <include file-name="QPixmap" location="global"/>
      <include file-name="qobjectconnect.h" location="global"/>
    </extra-includes>
    <add-function signature="open(PyCallable*@functor@)">
        <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp"
                     snippet="qmessagebox-open-connect-accept"/>
    </add-function>
    <!-- FIXME PYSIDE-7: Remove deprecated overloads -->
    <modify-function signature="critical(QWidget*,const QString&amp;,const QString&amp;,QFlags&lt;QMessageBox::StandardButton&gt;,QMessageBox::StandardButton)" allow-thread="yes"/>
    <modify-function signature="critical(QWidget*,const QString&amp;,const QString&amp;,QMessageBox::StandardButton,QMessageBox::StandardButton)"
                     allow-thread="yes"/>
    <modify-function signature="information(QWidget*,const QString&amp;,const QString&amp;,QFlags&lt;QMessageBox::StandardButton&gt;,QMessageBox::StandardButton)" allow-thread="yes"/>
    <modify-function signature="information(QWidget*,const QString&amp;,const QString&amp;,QMessageBox::StandardButton,QMessageBox::StandardButton)"
                     allow-thread="yes"/>
    <modify-function signature="question(QWidget*,const QString&amp;,const QString&amp;,QFlags&lt;QMessageBox::StandardButton&gt;,QMessageBox::StandardButton)" allow-thread="yes"/>
    <modify-function signature="question(QWidget*,const QString&amp;,const QString&amp;,QMessageBox::StandardButton,QMessageBox::StandardButton)"
                     allow-thread="yes"/>
    <modify-function signature="warning(QWidget*,const QString&amp;,const QString&amp;,QFlags&lt;QMessageBox::StandardButton&gt;,QMessageBox::StandardButton)" allow-thread="yes"/>
    <modify-function signature="warning(QWidget*,const QString&amp;,const QString&amp;,QMessageBox::StandardButton,QMessageBox::StandardButton)"
                     allow-thread="yes"/>
    <modify-function signature="QMessageBox(const QString&amp;,const QString&amp;,QMessageBox::Icon,int,int,int,QWidget*,QFlags&lt;Qt::WindowType&gt;)" remove="all"/>
    <modify-function signature="critical(QWidget*,const QString&amp;,const QString&amp;,int,int,int)" remove="all"/>
    <modify-function signature="critical(QWidget*,const QString&amp;,const QString&amp;,const QString&amp;,const QString&amp;,const QString&amp;,int,int)" remove="all"/>
    <modify-function signature="information(QWidget*,const QString&amp;,const QString&amp;,int,int,int)" remove="all"/>
    <modify-function signature="information(QWidget*,const QString&amp;,const QString&amp;,const QString&amp;,const QString&amp;,const QString&amp;,int,int)" remove="all"/>
    <modify-function signature="question(QWidget*,const QString&amp;,const QString&amp;,int,int,int)" remove="all"/>
    <modify-function signature="question(QWidget*,const QString&amp;,const QString&amp;,const QString&amp;,const QString&amp;,const QString&amp;,int,int)" remove="all"/>
    <modify-function signature="warning(QWidget*,const QString&amp;,const QString&amp;,int,int,int)" remove="all"/>
    <modify-function signature="warning(QWidget*,const QString&amp;,const QString&amp;,const QString&amp;,const QString&amp;,const QString&amp;,int,int)" remove="all"/>
    <modify-function signature="about(QWidget*,const QString&amp;,const QString&amp;)" allow-thread="yes"/>
    <modify-function signature="aboutQt(QWidget*,const QString&amp;)" allow-thread="yes"/>
  </object-type>
  <object-type name="QAbstractSpinBox">
    <enum-type name="ButtonSymbols"/>
    <enum-type name="CorrectionMode"/>
    <enum-type name="StepEnabledFlag" flags="StepEnabled"/>
    <enum-type name="StepType"/>
    <modify-function signature="setLineEdit(QLineEdit*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="fixup(QString &amp;)const">
      <modify-argument index="return">
        <replace-type modified-type="QString"/>
      </modify-argument>
      <inject-code class="target" position="end" file="../glue/qtcore.cpp" snippet="qstring-return"/>
    </modify-function>
    <modify-function signature="validate(QString &amp;,int &amp;)const">
      <modify-argument index="return">
        <replace-type modified-type="PyObject"/>
        <conversion-rule class="native">
            <insert-template name="validator_conversionrule"/>
        </conversion-rule>
      </modify-argument>
      <inject-code class="target" position="end">
        <insert-template name="return_tuple_QValidator_QString_int"/>
      </inject-code>
    </modify-function>
  </object-type>

  <object-type name="QTabWidget">
    <enum-type name="TabPosition"/>
    <enum-type name="TabShape"/>
    <modify-function signature="addTab(QWidget*,const QString&amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addTab(QWidget*,const QIcon&amp;,const QString&amp;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertTab(int,QWidget*,const QIcon&amp;,const QString&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertTab(int,QWidget*,const QString&amp;)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setCornerWidget(QWidget*,Qt::Corner)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setTabBar(QTabBar*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <!-- This function need be re-implemented in inject code -->
    <modify-function signature="removeTab(int)">
        <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qtabwidget-removetab"/>
    </modify-function>
    <modify-function signature="clear()">
        <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qtabwidget-clear"/>
    </modify-function>
  </object-type>
  <object-type name="QDateTimeEdit">
    <enum-type name="Section" flags="Sections"/>
    <modify-function signature="setCalendarWidget(QCalendarWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QSlider">
      <enum-type name="TickPosition"/>
  </object-type>
  <object-type name="QProgressDialog">
    <modify-function signature="setBar(QProgressBar*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setLabel(QLabel*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setCancelButton(QPushButton*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QLabel">
    <modify-function signature="setBuddy(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="pixmap(Qt::ReturnByValueConstant)const" remove="all"/>
    <modify-function signature="picture(Qt::ReturnByValueConstant)const" remove="all"/>
    <!-- // FIXME PYSIDE7: PYSIDE-2808 Remove (resource cleanup)? -->
    <modify-function signature="setMovie(QMovie *)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>

  <object-type name="QFileDialog">
    <enum-type name="AcceptMode"/>
    <enum-type name="DialogLabel"/>
    <enum-type name="FileMode"/>
    <enum-type name="Option" flags="Options"/>
    <enum-type name="ViewMode"/>
    <extra-includes>
      <include file-name="QUrl" location="global"/>
      <include file-name="QAbstractProxyModel" location="global"/>
    </extra-includes>
    <modify-function signature="setIconProvider(QAbstractFileIconProvider*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItemDelegate(QAbstractItemDelegate*)">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
    </modify-function>

    <modify-function signature="getExistingDirectory(QWidget*,const QString&amp;,const QString&amp;,QFlags&lt;QFileDialog::Option>)" allow-thread="yes"/>
    <modify-function signature="getExistingDirectoryUrl(QWidget*,const QString&amp;,const QUrl&amp;,QFlags&lt;QFileDialog::Option>,const QStringList&amp;)" allow-thread="yes"/>
    <modify-function signature="getOpenFileName(QWidget*,const QString&amp;,const QString&amp;,const QString&amp;,QString*,QFlags&lt;QFileDialog::Option&gt;)" allow-thread="yes">
      <modify-argument index="return"  pyi-type="Tuple[str, str]">
        <replace-type modified-type="(fileName, selectedFilter)"/>
      </modify-argument>
      <modify-argument index="5">
        <replace-type modified-type="QString"/>
        <replace-default-expression with="QString()"/>
      </modify-argument>
      <inject-code class="target" position="end" file="../glue/qtwidgets.cpp" snippet="qfiledialog-return" />
    </modify-function>
    <modify-function signature="getOpenFileNames(QWidget*,const QString&amp;,const QString&amp;,const QString&amp;,QString*,QFlags&lt;QFileDialog::Option&gt;)" allow-thread="yes">
      <modify-argument index="return" pyi-type="Tuple[List[str], str]">
        <replace-type modified-type="(fileNames, selectedFilter)"/>
      </modify-argument>
      <modify-argument index="5">
        <replace-type modified-type="QString"/>
        <replace-default-expression with="QString()"/>
      </modify-argument>
      <inject-code class="target" position="end" file="../glue/qtwidgets.cpp" snippet="qfiledialog-return" />
    </modify-function>

    <modify-function signature="getOpenFileUrl(QWidget*,const QString&amp;,const QUrl&amp;,const QString&amp;,QString*,QFlags&lt;QFileDialog::Option&gt;,const QStringList&amp;)" allow-thread="yes">
      <modify-argument index="return" pyi-type="Tuple[PySide6.QtCore.QUrl, str]">
        <replace-type modified-type="(fileName, selectedFilter)"/>
      </modify-argument>
      <modify-argument index="5">
        <replace-type modified-type="QString"/>
        <replace-default-expression with="QString()"/>
      </modify-argument>
      <inject-code class="target" position="end" file="../glue/qtwidgets.cpp" snippet="qfiledialog-return" />
    </modify-function>

    <modify-function signature="getOpenFileUrls(QWidget*,const QString&amp;,const QUrl&amp;,const QString&amp;,QString*,QFlags&lt;QFileDialog::Option&gt;,const QStringList&amp;)" allow-thread="yes">
      <modify-argument index="return" pyi-type="Tuple[List[PySide6.QtCore.QUrl], str]">
        <replace-type modified-type="(fileName, selectedFilter)"/>
      </modify-argument>
      <modify-argument index="5">
        <replace-type modified-type="QString"/>
        <replace-default-expression with="QString()"/>
      </modify-argument>
      <inject-code class="target" position="end" file="../glue/qtwidgets.cpp" snippet="qfiledialog-return" />
    </modify-function>

    <modify-function signature="getSaveFileName(QWidget*,const QString&amp;,const QString&amp;,const QString&amp;,QString*,QFlags&lt;QFileDialog::Option&gt;)" allow-thread="yes">
      <modify-argument index="return" pyi-type="Tuple[str, str]">
        <replace-type modified-type="(fileName, selectedFilter)"/>
      </modify-argument>
      <modify-argument index="5">
        <replace-type modified-type="QString"/>
        <replace-default-expression with="QString()"/>
      </modify-argument>
      <inject-code class="target" position="end" file="../glue/qtwidgets.cpp" snippet="qfiledialog-return" />
    </modify-function>

    <modify-function signature="getSaveFileUrl(QWidget*,const QString&amp;,const QUrl&amp;,const QString&amp;,QString*,QFlags&lt;QFileDialog::Option&gt;,const QStringList&amp;)" allow-thread="yes">
      <modify-argument index="return" pyi-type="Tuple[PySide6.QtCore.QUrl, str]">
        <replace-type modified-type="(fileName, selectedFilter)"/>
      </modify-argument>
      <modify-argument index="5">
        <replace-type modified-type="QString"/>
        <replace-default-expression with="QString()"/>
      </modify-argument>
      <inject-code class="target" position="end" file="../glue/qtwidgets.cpp" snippet="qfiledialog-return" />
    </modify-function>

  </object-type>

  <object-type name="QErrorMessage"/>
  <object-type name="QTabBar">
    <extra-includes>
      <include file-name="QIcon" location="global"/>
    </extra-includes>
    <enum-type name="Shape"/>
    <enum-type name="SelectionBehavior"/>
    <enum-type name="ButtonPosition"/>
  </object-type>
  <object-type name="QRadioButton"/>
  <object-type name="QScrollBar"/>
  <object-type name="QAbstractScrollArea">
    <enum-type name="SizeAdjustPolicy"/>
    <modify-function signature="setViewport(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addScrollBarWidget(QWidget*,QFlags&lt;Qt::AlignmentFlag&gt;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setCornerWidget(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setHorizontalScrollBar(QScrollBar*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setVerticalScrollBar(QScrollBar*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setViewport(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setupViewport(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="viewportEvent(QEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
  </object-type>
  <object-type name="QRubberBand">
    <enum-type name="Shape"/>
    <modify-function signature="QRubberBand(QRubberBand::Shape,QWidget*)">
      <modify-argument index="this">
        <parent index="2" action="add"/>
      </modify-argument>
      <modify-argument index="2">
        <rename to="parent"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QTableWidget">
    <modify-function signature="mimeData(const QList&lt;QTableWidgetItem*&gt;&amp;)const">
      <modify-argument index="return">
        <define-ownership class="native" owner="c++"/>
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setHorizontalHeaderItem(int,QTableWidgetItem*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItem(int,int,QTableWidgetItem*)">
      <modify-argument index="3">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="takeHorizontalHeaderItem(int)">
      <modify-argument index="return">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="takeVerticalHeaderItem(int)">
      <modify-argument index="return">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="takeItem(int,int)">
      <modify-argument index="return">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItemPrototype(const QTableWidgetItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setVerticalHeaderItem(int,QTableWidgetItem*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setCellWidget(int,int,QWidget*)" allow-thread="yes">
      <modify-argument index="3">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="removeCellWidget(int,int)" allow-thread="yes"/>
    <modify-function signature="setCurrentItem(QTableWidgetItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setCurrentItem(QTableWidgetItem*,QFlags&lt;QItemSelectionModel::SelectionFlag&gt;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QSplitter">
    <modify-function signature="getRange(int,int*,int*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="fix_args,number*,number*">
                <replace from="$TYPE" to="int"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="addWidget(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertWidget(int,QWidget*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QGroupBox">
    <modify-function signature="clicked(bool)" allow-thread="yes"/>
  </object-type>
  <object-type name="QStackedWidget">
    <modify-function signature="addWidget(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertWidget(int,QWidget*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="removeWidget(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setCurrentWidget(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QSplitterHandle"/>
  <object-type name="QDial"/>
  <object-type name="QKeySequenceEdit"/>
  <object-type name="QLineEdit">
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp" snippet="qwidget-addaction-glue"/>
    <enum-type name="ActionPosition"/>
    <enum-type name="EchoMode"/>
    <modify-function signature="setCompleter(QCompleter*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setValidator(const QValidator*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="del()" rename="del_"/>

    <add-function signature="addAction(QAction*@action@)">
      <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qlineedit-addaction"/>
    </add-function>

  </object-type>
  <object-type name="QLCDNumber">
      <enum-type name="Mode"/>
      <enum-type name="SegmentStyle"/>
  </object-type>
  <object-type name="QSplashScreen">
    <!-- Override QWidget.painter -->
    <modify-function signature="repaint()" remove="all"/>
    <modify-function signature="drawContents(QPainter*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
  </object-type>
  <object-type name="QDockWidget">
    <enum-type name="DockWidgetFeature" flags="DockWidgetFeatures"/>
    <modify-function signature="setTitleBarWidget(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setWidget(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QFrame">
      <enum-type name="Shadow" python-type="IntEnum"/>
      <enum-type name="Shape" python-type="IntEnum"/>
      <enum-type name="StyleMask"/>
  </object-type>
  <object-type name="QSpinBox"/>
  <object-type name="QTextBrowser"/>
  <object-type name="QDoubleSpinBox"/>
  <object-type name="QButtonGroup">
    <modify-function signature="addButton(QAbstractButton*,int)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
        <no-null-pointer/>
      </modify-argument>
    </modify-function>
    <modify-function signature="removeButton(QAbstractButton*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
        <no-null-pointer/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setId(QAbstractButton*,int)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QToolBar">
    <modify-function signature="addSeparator()">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addWidget(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertWidget(QAction*,QWidget*)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertSeparator(QAction*)">
      <modify-argument index="return">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="clear()">
      <inject-code file="../glue/qtwidgets.cpp" snippet="qtoolbar-clear"/>
    </modify-function>
  </object-type>
  <object-type name="QComboBox">
    <enum-type name="InsertPolicy"/>
    <enum-type name="SizeAdjustPolicy"/>
    <modify-function signature="setCompleter(QCompleter*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setValidator(const QValidator*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItemDelegate(QAbstractItemDelegate*)">
      <modify-argument index="1">
        <reference-count action="set"/>
        <no-null-pointer/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setView(QAbstractItemView*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setLineEdit(QLineEdit*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setModel(QAbstractItemModel*)">
      <modify-argument index="1">
        <no-null-pointer/>
        <reference-count action="set"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QTextEdit">
    <enum-type name="AutoFormattingFlag" flags="AutoFormatting"/>
    <enum-type name="LineWrapMode"/>
    <value-type name="ExtraSelection" >
      <include file-name="QTextEdit" location="global"/>
    </value-type>
    <extra-includes>
      <include file-name="QTextCursor" location="global"/>
    </extra-includes>
    <modify-function signature="createMimeDataFromSelection() const">
      <modify-argument index="return">
        <define-ownership class="native" owner="c++"/>
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setDocument(QTextDocument*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertFromMimeData(const QMimeData*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="print(QPagedPaintDevice*)const" rename="print_"/>
  </object-type>

  <object-type name="QApplication">
    <extra-includes>
      <include file-name="QBasicTimer" location="global"/>
      <include file-name="QFont" location="global"/>
      <include file-name="QFontMetrics" location="global"/>
      <include file-name="QPalette" location="global"/>
      <include file-name="QIcon" location="global"/>
      <include file-name="QLocale" location="global"/>
      <include file-name="QStyle" location="global"/>
      <include file-name="pysideqapp.h" location="global"/>
      <include file-name="pysidecleanup.h" location="global"/>
    </extra-includes>
    <modify-function signature="QApplication(int&amp;,char**,int)" access="private"/>
    <add-function signature="QApplication(QStringList@arguments@)">
        <inject-code file="../glue/qtwidgets.cpp" snippet="qapplication-1"/>
    </add-function>
    <add-function signature="QApplication()">
        <inject-code file="../glue/qtwidgets.cpp" snippet="qapplication-2"/>
    </add-function>
    <modify-function signature="setStyle(QStyle*)">
      <inject-code class="target" position="end" file="../glue/qtwidgets.cpp" snippet="qapplication-setStyle"/>
    </modify-function>
    <modify-function signature="exec()" allow-thread="yes"/>
    <add-function signature="exec_()" return-type="int">
        <inject-code file="../glue/qtwidgets.cpp" snippet="qapplication-exec"/>
    </add-function>
    <modify-function signature="notify(QObject*,QEvent*)" allow-thread="yes"/>
    <modify-function signature="alert(QWidget*,int)" allow-thread="yes"/>
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp" snippet="qapplication-init"/>
  </object-type>

  <object-type name="QCommandLinkButton"/>
  <!-- FIXME PYSIDE7: Move to QtGui -->
  <object-type name="QFileSystemModel" polymorphic-id-expression="qobject_cast&lt;QFileSystemModel*&gt;(%B)">
    <enum-type name="Roles" python-type="IntEnum"/>
    <enum-type name="Option" flags="Options"/>
    <modify-function signature="setIconProvider(QAbstractFileIconProvider*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QFormLayout">
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp"
                 snippet="qwidget-retrieveobjectname"/>
    <inject-code class="native" position="beginning" file="../glue/qtwidgets.cpp" snippet="qlayout-help-functions"/>

    <enum-type name="FieldGrowthPolicy"/>
    <enum-type name="ItemRole"/>
    <enum-type name="RowWrapPolicy"/>

    <value-type name="TakeRowResult">
      <include file-name="QFormLayout" location="global"/>
    </value-type>

    <modify-function signature="getLayoutPosition(QLayout*,int*,QFormLayout::ItemRole*)const">
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="return" pyi-type="Tuple[int, PySide6.QtWidgets.QFormLayout.ItemRole]">
            <replace-type modified-type="PyTuple"/>
        </modify-argument>
        <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qformlayout-fix-args" />
    </modify-function>
    <modify-function signature="getWidgetPosition(QWidget*,int*,QFormLayout::ItemRole*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qformlayout-fix-args" />
    </modify-function>
    <modify-function signature="getItemPosition(int,int*,QFormLayout::ItemRole*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <inject-code class="target" position="beginning" file="../glue/qtwidgets.cpp" snippet="qformlayout-fix-args" />
    </modify-function>

    <modify-function signature="addRow(QWidget*,QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addRow(QLayout*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addRow(QWidget*,QLayout*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addRow(QWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addRow(QString,QLayout*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addRow(QString,QWidget*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertRow(int,QLayout*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertRow(int,QWidget*,QLayout*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
      <modify-argument index="3">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertRow(int,QWidget*,QWidget*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
      <modify-argument index="3">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertRow(int,QWidget*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertRow(int,QString,QLayout*)">
      <modify-argument index="3">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertRow(int,QString,QWidget*)">
      <modify-argument index="3">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setLayout(int,QFormLayout::ItemRole,QLayout*)">
      <modify-argument index="3">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setWidget(int,QFormLayout::ItemRole,QWidget*)">
      <modify-argument index="3">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setItem(int,QFormLayout::ItemRole,QLayoutItem*)">
      <modify-argument index="3">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QGraphicsGridLayout" >
    <modify-function signature="addItem(QGraphicsLayoutItem*,int,int,QFlags&lt;Qt::AlignmentFlag&gt;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="addItem(QGraphicsLayoutItem*,int,int,int,int,QFlags&lt;Qt::AlignmentFlag&gt;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setAlignment(QGraphicsLayoutItem*,QFlags&lt;Qt::AlignmentFlag&gt;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QGraphicsLayout">
    <modify-function signature="getContentsMargins(qreal*,qreal*,qreal*,qreal*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*">
                <replace from="$TYPE" to="qreal"/>
            </insert-template>
        </inject-code>
        <inject-code class="native" position="end">
            <insert-template name="fix_native_return_number*,number*,number*,number*">
                <replace from="$TYPE" to="qreal"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="widgetEvent(QEvent*)">
        <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
  </object-type>
  <object-type name="QGraphicsLayoutItem" copyable="false" qt-register-metatype="base"
               parent-management="true">
    <modify-function signature="getContentsMargins(qreal*,qreal*,qreal*,qreal*)const">
        <modify-argument index="0">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
        </modify-argument>
        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*">
                <replace from="$TYPE" to="qreal"/>
            </insert-template>
        </inject-code>
        <inject-code class="native" position="end">
            <insert-template name="fix_native_return_number*,number*,number*,number*">
                <replace from="$TYPE" to="qreal"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="setParentLayoutItem(QGraphicsLayoutItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QGraphicsLinearLayout" >
    <modify-function signature="addItem(QGraphicsLayoutItem*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertItem(int,QGraphicsLayoutItem*)">
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="removeItem(QGraphicsLayoutItem*)">
      <modify-argument index="1">
        <parent index="this" action="remove"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setAlignment(QGraphicsLayoutItem*,QFlags&lt;Qt::AlignmentFlag&gt;)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setStretchFactor(QGraphicsLayoutItem*,int)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QGraphicsProxyWidget">
    <modify-function signature="QGraphicsProxyWidget(QGraphicsItem*,QFlags&lt;Qt::WindowType&gt;)">
      <modify-argument index="this">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="createProxyForChildWidget(QWidget*)">
      <modify-argument index="return">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="newProxyWidget(const QWidget*)">
      <modify-argument index="return">
        <parent index="1" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setWidget(QWidget*)" allow-thread="yes">
      <inject-code file="../glue/qtwidgets.cpp" snippet="qgraphicsproxywidget-setwidget"/>
    </modify-function>
  </object-type>
  <!-- a QObject so main-thread delete redundant -->
  <object-type name="QGraphicsWidget">
    <modify-function signature="getContentsMargins(qreal*,qreal*,qreal*,qreal*)const">
        <modify-argument index="return">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
        </modify-argument>

        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*">
                <replace from="$TYPE" to="qreal"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="getWindowFrameMargins(qreal*,qreal*,qreal*,qreal*)const">
        <modify-argument index="return">
            <replace-type modified-type="PyObject"/>
        </modify-argument>
        <modify-argument index="1">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="2">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="3">
            <remove-argument/>
        </modify-argument>
        <modify-argument index="4">
            <remove-argument/>
        </modify-argument>

        <inject-code class="target" position="beginning">
            <insert-template name="fix_number*,number*,number*,number*">
                <replace from="$TYPE" to="qreal"/>
            </insert-template>
        </inject-code>
    </modify-function>
    <modify-function signature="setLayout(QGraphicsLayout*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="changeEvent(QEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="closeEvent(QCloseEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="grabKeyboardEvent(QEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="grabMouseEvent(QEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="hideEvent(QHideEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="moveEvent(QGraphicsSceneMoveEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="paintWindowFrame(QPainter*,const QStyleOptionGraphicsItem*,QWidget*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="resizeEvent(QGraphicsSceneResizeEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="showEvent(QShowEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="ungrabKeyboardEvent(QEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="ungrabMouseEvent(QEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="windowFrameEvent(QEvent*)">
      <modify-argument index="1" invalidate-after-use="yes"/>
    </modify-function>
    <modify-function signature="setStyle(QStyle*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setTabOrder(QGraphicsWidget*,QGraphicsWidget*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
      <modify-argument index="2">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
  </object-type>
  <object-type name="QPlainTextDocumentLayout"/>
  <object-type name="QPlainTextEdit">
    <enum-type name="LineWrapMode"/>
    <modify-function signature="createMimeDataFromSelection() const">
      <modify-argument index="return">
        <define-ownership class="native" owner="c++"/>
        <define-ownership class="target" owner="default"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setDocument(QTextDocument*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="insertFromMimeData(const QMimeData*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="print(QPagedPaintDevice*)const" rename="print_"/>
  </object-type>
  <object-type name="QStyledItemDelegate">
    <modify-function signature="setItemEditorFactory(QItemEditorFactory*)">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setEditorData(QWidget*,QModelIndex)const">
      <modify-argument index="1">
        <parent index="this" action="add"/>
      </modify-argument>
    </modify-function>
    <modify-function signature="setModelData(QWidget*,QAbstractItemModel*,QModelIndex)const">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
    </modify-function>
  </object-type>

  <object-type name="QGesture" since="4.6">
      <enum-type name="GestureCancelPolicy"/>
  </object-type>
  <object-type name="QGestureRecognizer" since="4.6">
      <enum-type name="ResultFlag" flags="Result"/>
      <modify-function signature="create(QObject*)">
        <modify-argument index="return">
            <define-ownership owner="c++"/>
        </modify-argument>
      </modify-function>
      <modify-function signature="registerRecognizer(QGestureRecognizer*)">
        <modify-argument index="1">
            <define-ownership owner="c++"/>
        </modify-argument>
      </modify-function>
  </object-type>
  <object-type name="QTapAndHoldGesture" since="4.6"/>
  <object-type name="QTapGesture" since="4.6"/>
  <object-type name="QGraphicsAnchor" since="4.6"/>
  <object-type name="QGraphicsAnchorLayout" since="4.6"/>
  <object-type name="QGraphicsBlurEffect" since="4.6">
      <enum-type name="BlurHint" flags="BlurHints"/>
  </object-type>
  <object-type name="QGraphicsColorizeEffect" since="4.6"/>
  <object-type name="QGraphicsDropShadowEffect" since="4.6"/>

  <object-type name="QGraphicsEffect" since="4.6">
      <enum-type name="ChangeFlag" flags="ChangeFlags"/>
      <enum-type name="PixmapPadMode"/>
  </object-type>

  <object-type name="QGraphicsObject" since="4.6" default-superclass="QGraphicsItem"/>
  <object-type name="QGraphicsOpacityEffect" since="4.6"/>
  <object-type name="QGraphicsRotation" since="4.6"/>
  <object-type name="QGraphicsScale" since="4.6"/>
  <object-type name="QGraphicsTransform" since="4.6"/>
  <object-type name="QPanGesture" since="4.6"/>
  <object-type name="QPinchGesture" since="4.6">
      <enum-type name="ChangeFlag" flags="ChangeFlags"/>
  </object-type>

  <object-type name="QRhiWidget" since="6.7">
      <enum-type name="Api"/>
      <enum-type name="TextureFormat"/>
  </object-type>

  <object-type name="QSwipeGesture" since="4.6">
      <enum-type name="SwipeDirection"/>
  </object-type>

  <value-type name="QTileRules" since="4.6"/>

  <object-type name="QScroller">
      <enum-type name="State"/>
      <enum-type name="ScrollerGestureType"/>
      <enum-type name="Input"/>
  </object-type>
  <value-type name="QScrollerProperties">
      <enum-type name="OvershootPolicy"/>
      <enum-type name="FrameRates"/>
      <enum-type name="ScrollMetric"/>
  </value-type>

  <object-type name="QSizeGrip"/>

  <object-type name="QSystemTrayIcon">
    <enum-type name="ActivationReason"/>
    <enum-type name="MessageIcon"/>
    <modify-function signature="setContextMenu(QMenu*)">
      <modify-argument index="1">
        <reference-count action="set"/>
      </modify-argument>
    </modify-function>
  </object-type>

  <!-- The above entries may be present in the system or not. Keep this section organized. -->

  <suppress-warning text="signal 'activated' in class 'QCompleter' is overloaded."/>
  <suppress-warning text="signal 'highlighted' in class 'QCompleter' is overloaded."/>
</typesystem>
