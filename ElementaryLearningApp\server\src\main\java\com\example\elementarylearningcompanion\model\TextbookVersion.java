package com.example.elementarylearningcompanion.model;

import jakarta.persistence.*;

@Entity
@Table(name = "textbook_versions")
public class TextbookVersion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(nullable = false)
    private String name;

    @Column(nullable = false)
    private String subject;

    private String applicableGrades;

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getApplicableGrades() {
        return applicableGrades;
    }

    public void setApplicableGrades(String applicableGrades) {
        this.applicableGrades = applicableGrades;
    }
}
