package com.example.elementarylearningcompanion.viewmodel;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;
import com.example.elementarylearningcompanion.dto.Character;
import com.example.elementarylearningcompanion.dto.Lesson;
import com.example.elementarylearningcompanion.dto.TextbookVersion;
import com.example.elementarylearningcompanion.network.ApiService;
import com.example.elementarylearningcompanion.network.RetrofitClient;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import java.util.List;

public class ContentViewModel extends ViewModel {
    private final ApiService apiService = RetrofitClient.getClient().create(ApiService.class);

    private final MutableLiveData<List<TextbookVersion>> _textbooks = new MutableLiveData<>();
    public LiveData<List<TextbookVersion>> textbooks = _textbooks;

    private final MutableLiveData<List<Lesson>> _lessons = new MutableLiveData<>();
    public LiveData<List<Lesson>> lessons = _lessons;

    private final MutableLiveData<Lesson> _lessonDetails = new MutableLiveData<>();
    public LiveData<Lesson> lessonDetails = _lessonDetails;

    private final MutableLiveData<List<Character>> _characters = new MutableLiveData<>();
    public LiveData<List<Character>> characters = _characters;

    private final MutableLiveData<Boolean> _isLoading = new MutableLiveData<>(false);
    public LiveData<Boolean> isLoading = _isLoading;

    private final MutableLiveData<String> _error = new MutableLiveData<>();
    public LiveData<String> error = _error;

    public void fetchTextbooks(String subject) {
        _isLoading.setValue(true);
        apiService.getTextbookVersions(subject).enqueue(new Callback<List<TextbookVersion>>() {
            @Override
            public void onResponse(Call<List<TextbookVersion>> call, Response<List<TextbookVersion>> response) {
                if (response.isSuccessful()) {
                    _textbooks.postValue(response.body());
                } else {
                    _error.postValue("Failed to fetch textbooks: " + response.code());
                }
                _isLoading.setValue(false);
            }

            @Override
            public void onFailure(Call<List<TextbookVersion>> call, Throwable t) {
                _error.postValue("Network error: " + t.getMessage());
                _isLoading.setValue(false);
            }
        });
    }

    public void fetchLessons(int textbookVersionId, int grade) {
        _isLoading.setValue(true);
        apiService.getLessons(textbookVersionId, grade).enqueue(new Callback<List<Lesson>>() {
            @Override
            public void onResponse(Call<List<Lesson>> call, Response<List<Lesson>> response) {
                if (response.isSuccessful()) {
                    _lessons.postValue(response.body());
                } else {
                    _error.postValue("Failed to fetch lessons: " + response.code());
                }
                _isLoading.setValue(false);
            }

            @Override
            public void onFailure(Call<List<Lesson>> call, Throwable t) {
                _error.postValue("Network error: " + t.getMessage());
                _isLoading.setValue(false);
            }
        });
    }

    public void fetchLessonDetails(long lessonId) {
        _isLoading.setValue(true);
        apiService.getLessonDetails(lessonId).enqueue(new Callback<Lesson>() {
            @Override
            public void onResponse(Call<Lesson> call, Response<Lesson> response) {
                if (response.isSuccessful()) {
                    _lessonDetails.postValue(response.body());
                } else {
                    _error.postValue("Failed to fetch lesson details: " + response.code());
                }
                _isLoading.setValue(false);
            }

            @Override
            public void onFailure(Call<Lesson> call, Throwable t) {
                _error.postValue("Network error: " + t.getMessage());
                _isLoading.setValue(false);
            }
        });
    }

    public void fetchCharactersByLesson(long lessonId) {
        _isLoading.setValue(true);
        apiService.getCharactersByLesson(lessonId).enqueue(new Callback<List<Character>>() {
            @Override
            public void onResponse(Call<List<Character>> call, Response<List<Character>> response) {
                if (response.isSuccessful()) {
                    _characters.postValue(response.body());
                } else {
                    _error.postValue("Failed to fetch characters: " + response.code());
                }
                _isLoading.setValue(false);
            }

            @Override
            public void onFailure(Call<List<Character>> call, Throwable t) {
                _error.postValue("Network error: " + t.getMessage());
                _isLoading.setValue(false);
            }
        });
    }
}
