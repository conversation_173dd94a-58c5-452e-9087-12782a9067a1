package com.example.elementarylearningcompanion.service;

import com.example.elementarylearningcompanion.model.Character;
import com.example.elementarylearningcompanion.model.Lesson;
import com.example.elementarylearningcompanion.model.TextbookVersion;
import com.example.elementarylearningcompanion.repository.CharacterRepository;
import com.example.elementarylearningcompanion.repository.LessonRepository;
import com.example.elementarylearningcompanion.repository.TextbookVersionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ContentService {

    @Autowired
    private TextbookVersionRepository textbookVersionRepository;

    @Autowired
    private LessonRepository lessonRepository;

    @Autowired
    private CharacterRepository characterRepository;

    public List<TextbookVersion> getAllTextbookVersions() {
        return textbookVersionRepository.findAll();
    }

    public List<TextbookVersion> getTextbookVersionsBySubject(String subject) {
        return textbookVersionRepository.findBySubject(subject);
    }

    public List<Lesson> getLessons(Integer textbookVersionId, Integer grade) {
        return lessonRepository.findByTextbookVersionIdAndGrade(textbookVersionId, grade);
    }

    public Optional<Lesson> getLessonDetails(Long lessonId) {
        // This will fetch the lesson and its sentences due to eager/transactional loading
        return lessonRepository.findById(lessonId);
    }

    public List<Character> getCharactersByLesson(Long lessonId) {
        return characterRepository.findByLessonId(lessonId);
    }
}
