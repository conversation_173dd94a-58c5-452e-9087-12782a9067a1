1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.elementarylearningcompanion"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:5:5-67
11-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:5:22-64
12
13    <permission
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
14        android:name="com.example.elementarylearningcompanion.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
14-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
15        android:protectionLevel="signature" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
16
17    <uses-permission android:name="com.example.elementarylearningcompanion.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
18
19    <application
19-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:7:5-37:19
20        android:allowBackup="true"
20-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:8:9-35
21        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
21-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a90387b92ad49178bbd95d8e99c39d57\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
22        android:dataExtractionRules="@xml/data_extraction_rules"
22-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:9:9-65
23        android:debuggable="true"
24        android:extractNativeLibs="false"
25        android:fullBackupContent="@xml/backup_rules"
25-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:10:9-54
26        android:label="@string/app_name"
26-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:11:9-41
27        android:supportsRtl="true"
27-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:12:9-35
28        android:testOnly="true"
29        android:theme="@style/Theme.ElementaryLearningCompanion"
29-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:13:9-65
30        android:usesCleartextTraffic="true" >
30-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:14:9-44
31        <activity
31-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:16:9-26:20
32            android:name="com.example.elementarylearningcompanion.MainActivity"
32-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:17:13-41
33            android:exported="true"
33-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:18:13-36
34            android:label="@string/app_name"
34-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:19:13-45
35            android:theme="@style/Theme.ElementaryLearningCompanion" >
35-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:20:13-69
36            <intent-filter>
36-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:21:13-25:29
37                <action android:name="android.intent.action.MAIN" />
37-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:22:17-69
37-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:22:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:24:17-77
39-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:24:27-74
40            </intent-filter>
41        </activity>
42        <activity
42-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:27:9-31:72
43            android:name="com.example.elementarylearningcompanion.OnboardingActivity"
43-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:28:13-47
44            android:exported="false"
44-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:29:13-37
45            android:label="初始设置"
45-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:30:13-33
46            android:theme="@style/Theme.ElementaryLearningCompanion" />
46-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:31:13-69
47        <activity
47-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:32:9-36:72
48            android:name="com.example.elementarylearningcompanion.LessonActivity"
48-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:33:13-43
49            android:exported="false"
49-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:34:13-37
50            android:label="课文学习"
50-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:35:13-33
51            android:theme="@style/Theme.ElementaryLearningCompanion" />
51-->F:\study\ElementaryLearningApp\client\app\src\main\AndroidManifest.xml:36:13-69
52        <activity
52-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:23:9-25:39
53            android:name="androidx.activity.ComponentActivity"
53-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:24:13-63
54            android:exported="true" />
54-->[androidx.compose.ui:ui-test-manifest:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\c159823cd1d5b89649bf0a3c97706e19\transformed\ui-test-manifest-1.6.7\AndroidManifest.xml:25:13-36
55        <activity
55-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
56            android:name="androidx.compose.ui.tooling.PreviewActivity"
56-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
57            android:exported="true" />
57-->[androidx.compose.ui:ui-tooling-android:1.6.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\81e6a57d5e25883e08f3f201e44bac3c\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
58
59        <provider
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
60            android:name="androidx.startup.InitializationProvider"
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
61            android:authorities="com.example.elementarylearningcompanion.androidx-startup"
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
62            android:exported="false" >
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
63            <meta-data
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.emoji2.text.EmojiCompatInitializer"
64-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
65                android:value="androidx.startup" />
65-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\606ddcf2ed54d0de7bd61c6cbdf01b4f\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
66            <meta-data
66-->[androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
67-->[androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:30:17-78
68                android:value="androidx.startup" />
68-->[androidx.lifecycle:lifecycle-process:2.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a4e46d952fb9e2f7c963435e77e0cc10\transformed\lifecycle-process-2.8.0\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
71                android:value="androidx.startup" />
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
72        </provider>
73
74        <receiver
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
75            android:name="androidx.profileinstaller.ProfileInstallReceiver"
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
76            android:directBootAware="false"
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
77            android:enabled="true"
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
78            android:exported="true"
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
79            android:permission="android.permission.DUMP" >
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
80            <intent-filter>
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
81                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
82            </intent-filter>
83            <intent-filter>
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
84                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
85            </intent-filter>
86            <intent-filter>
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
87                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
88            </intent-filter>
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
90                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a715daf433f813211a4035e9661cf325\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
91            </intent-filter>
92        </receiver>
93    </application>
94
95</manifest>
