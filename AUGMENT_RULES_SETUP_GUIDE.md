# 🎯 Augment Rules配置指南 - 实现类似Cursor的.cursorrules功能

## 📋 概述

Augment通过**Guidelines**功能实现类似Cursor的`.cursorrules`功能，让您可以自定义AI的行为规范和编码偏好。

## 🚀 两种配置方式

### 方式一：项目级别Guidelines（推荐）

#### 1. 创建 `.augment-guidelines` 文件
在项目根目录创建 `.augment-guidelines` 文件：

```bash
# 在项目根目录
touch .augment-guidelines
```

#### 2. 编写规则内容
参考我们提供的 `.augment-guidelines` 文件模板，包含：
- 核心开发原则
- 技术栈偏好
- 架构模式
- 代码规范
- 工具使用规则
- 文档要求
- 测试要求

#### 3. 提交到版本控制
```bash
git add .augment-guidelines
git commit -m "Add Augment guidelines for project"
```

### 方式二：用户级别Guidelines

#### 1. 打开Augment Chat
在VS Code中打开Augment面板

#### 2. 访问Guidelines设置
- 点击Context菜单（📎图标）
- 或者在输入框中输入 `@`
- 选择 "User Guidelines"

#### 3. 输入个人偏好
添加您的个人编码偏好和规则

#### 4. 保存设置
点击"Save"保存，这些规则将应用到所有项目

## 📊 功能对比

| 特性 | Cursor .cursorrules | Augment Guidelines |
|------|-------------------|-------------------|
| 配置文件 | `.cursorrules` | `.augment-guidelines` |
| 用户级配置 | ❌ | ✅ |
| 项目级配置 | ✅ | ✅ |
| 字符限制 | 无 | 2000字符 |
| 版本控制 | ✅ | ✅ |
| 团队共享 | ✅ | ✅ |
| 应用范围 | 全部功能 | Agent + Chat |

## 🎨 规则编写最佳实践

### ✅ 好的规则示例

```
- 使用 pytest 而不是 unittest 进行测试
- 函数名使用动词开头，清晰表达功能
- 完成重要任务后使用 interactive_feedback 工具请求确认
- 新增依赖前必须征求用户同意
```

### ❌ 避免的规则

```
- 写更短的回答（可能影响回答质量）
- 只返回代码（缺少解释）
- 过于复杂的规则（超过2000字符）
```

### 📝 规则编写技巧

1. **使用列表格式** - 清晰易读
2. **语言简洁明确** - 避免歧义
3. **具体而非抽象** - 给出具体的库名、模式等
4. **包含交互规则** - 何时使用interactive_feedback
5. **设置优先级** - 明确哪些规则更重要

## 🔧 高级配置

### 结合Interactive Feedback的规则

```
## 交互反馈规则
- 实现新功能后，使用 interactive_feedback 请求用户测试
- 修复问题后，让用户验证修复效果
- 进行重构前，先征求用户同意
- 安装新依赖前，请求用户确认
```

### 技术栈特定规则

```
## Python项目规则
- 使用 uv 而不是 pip 管理依赖
- 遵循 PEP 8 代码规范
- 使用 black 和 isort 格式化代码
- 添加 mypy 类型注解

## React项目规则
- 使用函数组件而不是类组件
- 优先使用 hooks 而不是 HOC
- 使用 TypeScript 进行类型检查
- 遵循 React 最佳实践
```

## 🧪 测试您的规则

### 1. 创建测试提示
```
请根据项目guidelines创建一个简单的Python函数，并解释你遵循了哪些规则。
```

### 2. 验证规则应用
检查Augment是否：
- 使用了指定的技术栈
- 遵循了代码规范
- 在适当时机使用interactive_feedback

### 3. 迭代改进
根据实际使用效果调整规则内容

## 📁 文件结构示例

```
project-root/
├── .augment-guidelines          # Augment规则文件
├── .cursorrules-style-guidelines # Cursor风格规则（参考）
├── README.md
├── src/
└── tests/
```

## 🎯 实际使用示例

### 项目初始化时
```
请根据.augment-guidelines中的规则，帮我初始化一个Python项目结构。
```

### 功能开发时
```
请实现用户登录功能，遵循项目guidelines，完成后使用interactive_feedback请求我的确认。
```

### 代码审查时
```
请审查这段代码是否符合项目guidelines，并提出改进建议。
```

## 🔄 规则更新流程

1. **修改规则文件** - 编辑 `.augment-guidelines`
2. **测试新规则** - 用简单任务验证
3. **团队讨论** - 与团队成员确认规则
4. **提交更改** - 通过Git版本控制
5. **通知团队** - 确保所有人了解新规则

## 📞 故障排除

### 规则不生效
- 确认文件名正确：`.augment-guidelines`
- 检查文件是否在项目根目录
- 重启Augment扩展
- 验证规则字符数不超过2000

### 规则冲突
- 项目级规则优先于用户级规则
- 具体规则优先于通用规则
- 新规则覆盖旧规则

---

**现在您可以像使用Cursor的.cursorrules一样，在Augment中自定义AI的行为规范了！** 🎉
