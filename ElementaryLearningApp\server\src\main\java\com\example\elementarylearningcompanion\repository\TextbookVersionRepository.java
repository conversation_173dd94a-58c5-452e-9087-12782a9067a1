package com.example.elementarylearningcompanion.repository;

import com.example.elementarylearningcompanion.model.TextbookVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TextbookVersionRepository extends JpaRepository<TextbookVersion, Integer> {

    List<TextbookVersion> findBySubject(String subject);

}
